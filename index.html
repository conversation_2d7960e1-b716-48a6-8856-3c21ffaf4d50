<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/png" href="./assets/icons/favicon.png">
    <title>启动待办</title>
    <style>
        /* --- <PERSON>, Page, Button, Input, Settings Styles (Keep Existing) --- */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        }

        html,
        body {
            height: 100%;
            width: 100%;
            overflow: hidden;
            background-color: #eef5ff;
        }

        .page {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.5s ease-in-out, visibility 0s linear 0.5s;
            padding: 20px;
            /* Padding for page content */
        }

        .page.active {
            opacity: 1;
            visibility: visible;
            transition: opacity 0.5s ease-in-out;
        }

        button {
            padding: 10px 18px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background-color: #4a90e2;
            color: white;
            transition: background-color 0.2s ease, transform 0.1s ease;
            margin-top: 10px;
        }

        button:hover:not(:disabled) {
            background-color: #357abd;
        }

        button:active:not(:disabled) {
            transform: scale(0.98);
        }

        button:disabled {
            background-color: #c0c0c0;
            cursor: not-allowed;
        }

        input[type="text"],
        textarea,
        input[type="url"] {
            padding: 10px;
            font-size: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 100%;
            margin-bottom: 10px;
        }

        input[type="text"]:disabled {
            background-color: #f0f0f0;
            cursor: not-allowed;
        }

        textarea {
            line-height: 1.5;
        }

        .widget-controls {
            position: absolute;
            bottom: 5px;
            right: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .settings-icon,
        .reset-icon {
            font-size: 18px;
            cursor: pointer;
            color: #aaa;
            padding: 5px;
            line-height: 1;
        }

        .settings-icon:hover,
        .reset-icon:hover {
            color: #555;
        }

        .reset-icon {
            font-size: 20px;
        }

        .settings-panel {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            z-index: 10;
            bottom: 35px;
            right: 5px;
            width: 250px;
            font-size: 14px;
            max-height: calc(100vh - 100px);
            overflow-y: auto;
        }

        .settings-panel label {
            display: block;
            margin-bottom: 3px;
            font-weight: bold;
            color: #333;
        }

        .settings-panel input[type="text"],
        .settings-panel input[type="url"] {
            margin-bottom: 8px;
            width: 100%;
        }

        .settings-panel button {
            font-size: 14px;
            padding: 5px 10px;
            margin-top: 5px;
            margin-right: 5px;
        }

        /* --- Page 1 Styles (Button Positioning Updated) --- */
        #page1 .content-box {
            background-color: white;
            padding: 50px 60px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
            text-align: center;
            position: relative;
            max-width: 480px;
            width: 90%;
        }

        #page1 h1 {
            color: #333;
            margin-bottom: 15px;
            font-size: 26px;
        }

        #page1 .subtitle {
            color: #666;
            margin-bottom: 35px;
            font-size: 17px;
        }

        #page1 h3 {
            color: #444;
            margin-bottom: 15px;
            font-size: 19px;
            text-align: left;
        }

        #page1 #p1-todo-input {
            margin-bottom: 20px;
        }

        #page1 #p1-todo-input.completed {
            text-decoration: line-through;
            color: #888;
            background-color: #f8f8f8;
        }

        #page1 #p1-complete-btn {
            width: 100%;
            padding: 12px 18px;
        }

        #page1 #p1-celebration {
            font-size: 80px;
        }

        #page1 #next-page-btn {
            position: absolute;
            bottom: 15px;
            right: 15px;
            background: none;
            border: none;
            font-size: 30px;
            color: #4a90e2;
            cursor: pointer;
            padding: 0;
            margin: 0;
            z-index: 20;
            line-height: 1;
        }

        #page1 #next-page-btn:hover {
            color: #357abd;
        }

        #page1 .data-tools {
            position: absolute;
            bottom: 15px;
            left: 15px;
            display: flex;
            gap: 5px;
            z-index: 20;
            margin: 0;
        }

        #page1 .data-tools button {
            background: none;
            border: 1px solid #aaa;
            color: #777;
            font-size: 14px;
            border-radius: 4px;
            padding: 3px 8px;
            margin: 0;
            margin-top: 0 !important;
        }

        #page1 .data-tools button:hover {
            background-color: #eee;
            color: #333;
            border-color: #888;
        }

        /* NEW: Hide intro button initially */
        #hide-intro-btn {
            display: none;
        }

        #import-file-input {
            display: none;
        }


        /* General Feedback Animation (Keep Existing) */
        .feedback-animation {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.5);
            font-size: 60px;
            opacity: 0;
            transition: transform 0.4s cubic-bezier(0.18, 0.89, 0.32, 1.28), opacity 0.4s ease;
            pointer-events: none;
            z-index: 5;
        }

        .feedback-animation.animate {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }

        /* --- Page 2 Styles (Widget Alignment Updated) --- */
        #back-to-page1-btn {
            position: absolute;
            bottom: 15px;
            left: 15px;
            background: none;
            border: 1px solid #aaa;
            color: #777;
            font-size: 20px;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            line-height: 30px;
            text-align: center;
            cursor: pointer;
            padding: 0;
            margin: 0;
            z-index: 20;
        }

        #back-to-page1-btn:hover {
            background-color: #eee;
            color: #333;
            border-color: #888;
        }

        #next-page3-btn {
            position: absolute;
            bottom: 15px;
            right: 15px;
            background: none;
            border: 1px solid #aaa;
            color: #777;
            font-size: 20px;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            line-height: 30px;
            text-align: center;
            cursor: pointer;
            padding: 0;
            margin: 0;
            z-index: 20;
        }

        #next-page3-btn:hover {
            background-color: #eee;
            color: #333;
            border-color: #888;
        }

        #page2 .page2-container {
            display: flex;
            width: 100%;
            height: 100%;
            max-width: 1300px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 25px;
            gap: 25px;
            position: relative;
        }

        .column {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 0 10px;
            overflow-y: auto;
            position: relative;
            min-height: 300px;
        }

        .column h2 {
            text-align: center;
            color: #444;
            margin-bottom: 15px;
            font-size: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            flex-shrink: 0;
            /* MODIFIED: Make headers sticky */
            position: sticky;
            top: 0;
            background-color: white;
            z-index: 2;
        }

        .list-container {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .list-container:has(ul:empty):not(:has(.placeholder-msg)),
        .list-container:has(ul:empty):has(#no-tasks-msg:not([style*="display: none"])) {
            align-items: center;
            justify-content: center;
        }

        .list-container ul {
            list-style: none;
            width: 100%;
            padding: 0;
        }

        .placeholder-msg {
            color: #888;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }

        .input-area {
            margin-top: auto;
            padding-top: 15px;
            border-top: 1px solid #eee;
            display: flex;
            flex-direction: column;
            gap: 10px;
            background: white;
            position: sticky;
            bottom: 0;
            flex-shrink: 0;
        }

        .input-area input,
        .input-area textarea {
            width: 100%;
        }

        .input-area button {
            width: 100%;
            margin-top: 0;
        }

        #todo-actions {
            display: flex;
            justify-content: space-around;
            padding: 5px 0 15px 0;
            border-bottom: 1px dashed #eee;
            flex-shrink: 0;
        }

        #todo-actions button {
            padding: 6px 12px;
            font-size: 13px;
            margin: 0 5px;
            background-color: #6c757d;
        }

        #todo-actions button:hover:not(:disabled) {
            background-color: #5a6268;
        }

        #reset-todos-btn {
            /* This button text and function changes to open a modal */
            background-color: #ffc107;
            /* Example: Yellow for reset/arrange */
            color: white;
            /* 白色文字 */
        }

        #reset-todos-btn:hover:not(:disabled) {
            background-color: #e0a800;
        }

        #clear-todos-btn {
            background-color: #dc3545;
        }

        /* This button text and function changes */
        #clear-todos-btn:hover:not(:disabled) {
            background-color: #c82333;
        }

        #todo-column .list-container {
            align-items: initial;
            justify-content: initial;
        }

        .congrats-msg {
            color: #075b4b;
            font-weight: bold;
            text-align: center;
            padding: 3px 15px 6px 15px;
            display: none;
        }

        .todo-item {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            position: relative;
            min-height: 50px;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
            /* Smooth transition for selection */
            cursor: pointer;
            /* Indicate the item body is clickable */
        }

        /* Style for time block dividers */
        .time-block-divider {
            text-align: center !important;
            color: #888;
            font-style: normal;
            /* 不要斜体 */
            font-size: 12px;
            /* 更小的字体 */
            background-color: #f0f0f0 !important;
            /* Override todo-item background */
            cursor: default !important;
            border-top: 1px dashed #ccc;
            border-bottom: 1px dashed #ccc;
            margin-top: 2px !important;
            /* 减小上下间距 */
            margin-bottom: 10px !important;
            /* 减小上下间距 */
            padding: 4px 15px !important;
            /* 减小上下内边距 */
            user-select: none;
            /* Prevent text selection */
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-height: 25px !important;
            /* 设置最小高度 */
            height: 25px !important;
            /* 固定高度 */
            transition: border-color 0.5s ease-in-out;
            /* For animation */
        }

        @keyframes pulse-border {
            0% {
                border-top-color: #ffffff;
                border-bottom-color: #ffffff;
                border-left-color: #ffffff;
                border-right-color: #ffffff;
            }

            50% {
                border-top-color: #202020;
                border-bottom-color: #202020;
                border-left-color: #aaaaaa;
                border-right-color: #aaaaaa;
            }

            100% {
                border-top-color: #ffffff;
                border-bottom-color: #ffffff;
                border-left-color: #ffffff;
                border-right-color: #ffffff;
            }
        }

        .time-block-divider.current-time-block {
            /* animation: pulse-border 5s infinite ease-in-out; */
            border-right: #9fc0e3 dashed 2px;
            border-left: #9fc0e3 dashed 2px;
            text-shadow: 2px 2px 3px #9fc0e3    ;
        }


        @keyframes rainbow-text {
            0% {
                filter: hue-rotate(0deg) opacity(0.8);
            }

            50% {
                filter: hue-rotate(90deg) opacity(0.8);
            }

            100% {
                filter: hue-rotate(0deg) opacity(0.8);
            }
        }

        .time-block-divider.rainbow-mode {
            color: #c8c8c8cf;
            font-weight: bold;
            backdrop-filter:   blur(10px);
            /* animation: rainbow-text 50s linear infinite; */
        }


        .todo-item.rainbow-blur {
            filter: opacity(0.8) blur(0.2em) grayscale(0.85);
        }

        .level-indicator {
            font-size: 11px;
            color: #fff;
            background-color: #6c757d;
            border-radius: 3px;
            padding: 2px 5px;
            margin-right: 10px;
            font-weight: bold;
            flex-shrink: 0;
            cursor: pointer;
            user-select: none;
            border: 1px solid transparent;
            /* Base border for consistent layout */
        }

        /* NEW: Max level borders applied to level-indicator */
        .level-indicator.max-level-1 {
            border: 2px solid #6c757d;
        }

        .level-indicator.max-level-2 {
            border: 2px double #3498ff;
        }

        .level-indicator.max-level-3 {
            border: 2px double #9e71ed;
        }

        .level-indicator.max-level-4 {
            border: 2px double #f9afb6;
        }


        .level-indicator:hover {
            background-color: #5a6268;
        }

        .todo-item-text {
            flex-grow: 1;
            text-align: left;
            margin-right: 10px;
            word-break: break-word;
        }

        .todo-item-text.done {
            text-decoration: line-through;
            color: #aaa;
        }

        .todo-item-controls {
            display: flex;
            gap: 5px;
            flex-shrink: 0;
        }

        .todo-item-controls button {
            padding: 5px 8px;
            font-size: 12px;
            min-width: 40px;
            margin-top: 0;
            cursor: pointer;
        }

        /* Todo complete button level colors */
        .todo-item-controls .complete-btn.level-1 {
            background-color: #35a046cf;
            color: white;
        }

        .todo-item-controls .complete-btn.level-1:hover:not(:disabled) {
            background-color: #27873c;
        }

        .todo-item-controls .complete-btn.level-2 {
            background-color: #4a90e2;
            color: white;
        }

        .todo-item-controls .complete-btn.level-2:hover:not(:disabled) {
            background-color: #135aa6;
        }

        .todo-item-controls .complete-btn.level-3 {
            background-color: #7a57ba;
            color: white;
        }

        .todo-item-controls .complete-btn.level-3:hover:not(:disabled) {
            background-color: #5a32a3;
        }

        .todo-item-controls .complete-btn.level-4 {
            background-color: #eb515f;
            color: white;
        }

        .todo-item-controls .complete-btn.level-4:hover:not(:disabled) {
            background-color: #c82333;
        }

        /* 最大层级边框样式 - BORDER REMOVED FROM BUTTONS, MOVED TO .level-indicator */
        .todo-item-controls .complete-btn.max-level-1 {
            /* border: 1px outset #45d767; */
        }

        .todo-item-controls .complete-btn.max-level-2 {
            /* border: 1px outset #3498ff; */
        }

        .todo-item-controls .complete-btn.max-level-3 {
            /* border: 1px outset #8659d3; */
        }

        .todo-item-controls .complete-btn.max-level-4 {
            /* border: 1px outset #f55b6b; */
        }


        .todo-item-controls .undo-btn {
            background-color: #d4d4d4;
            color: #333;
        }

        .todo-item-controls .undo-btn:hover:not(:disabled) {
            background-color: #b4b4b4;
        }

        /* 撤销按钮也显示相应的最大层级边框 - BORDER REMOVED FROM BUTTONS, MOVED TO .level-indicator */
        .todo-item-controls .undo-btn.max-level-1 {
            /* border: 1px solid #45d767; */
        }

        .todo-item-controls .undo-btn.max-level-2 {
            /* border: 1px solid #3498ff; */
        }

        .todo-item-controls .undo-btn.max-level-3 {
            /* border: 1px solid #8659d3; */
        }

        .todo-item-controls .undo-btn.max-level-4 {
            /* border: 1px solid #f55b6b; */
        }


        #habit-column .list-container {
            align-items: initial;
            justify-content: initial;
        }

        /* 简化模式样式 */
        .simplified-mode .input-area {
            display: none;
        }

        .simplified-mode .column h2 {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .simplified-mode .column h2:hover {
            background-color: #f0f8ff;
        }

        /* 简化模式下隐藏习惯设置按钮 */
        .simplified-mode .habit-settings-icon {
            display: none;
        }

        /* 简化模式下习惯标题可点击 */
        .simplified-mode .habit-item h4 {
            cursor: pointer;
            transition: background-color 0.2s ease;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .simplified-mode .habit-item h4:hover {
            background-color: #e8f4fd;
        }

        /* 强制显示习惯设置面板的类 */
        .habit-item-settings.show {
            display: block !important;
        }

        .habit-item {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            /* Default border */
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            position: relative;
            transition: border-color 0.3s ease-in-out;
            /* Smooth transition for border change */
        }

        .habit-item.incremented-today {
            border-color: #f1d0b7;
            /* Slightly darker border when incremented today */
        }


        .habit-item h4 {
            margin-bottom: 5px;
            color: #333;
            font-size: 16px;
        }

        .habit-item p {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .habit-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
        }

        .habit-counter {
            display: flex;
            align-items: center;
        }

        .habit-counter span {
            color: #352d2d;
            font-weight: bold;
            font-size: 18px;
            margin: 0 15px;
            min-width: 25px;
            text-align: center;
        }

        .habit-counter button {
            padding: 3px 8px;
            font-size: 16px;
            line-height: 1;
            background-color: #a2a6aa;
            min-width: 25px;
            margin-top: 0;
        }

        .habit-counter button:hover {
            background-color: #5a6268;
        }

        .habit-settings-icon {
            cursor: pointer;
            color: #aaa;
            font-size: 16px;
            padding: 5px;
        }

        .habit-settings-icon:hover {
            color: #555;
        }

        .habit-item-settings {
            display: none;
            background-color: #f0f0f0;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            text-align: right;
        }

        .habit-item-settings button {
            font-size: 12px;
            padding: 4px 8px;
            margin-left: 5px;
            margin-top: 0;
        }

        .habit-item-settings button.edit-habit-btn {
            background-color: #ffc107;
            color: #333;
        }

        .habit-item-settings button.delete-habit-btn {
            background-color: #dc3545;
        }

        .habit-item.editing .habit-content,
        .habit-item.editing .habit-controls {
            display: none;
        }

        .habit-item-edit-form {
            display: none;
        }

        .habit-item.editing .habit-item-edit-form {
            display: block;
        }

        .habit-item-edit-form input,
        .habit-item-edit-form textarea {
            margin-bottom: 8px;
            font-size: 14px;
            padding: 8px;
        }

        .habit-item-edit-form textarea.folded-desc {
            display: none;
        }

        .habit-item-edit-form button {
            font-size: 13px;
            padding: 5px 10px;
            margin-top: 5px;
        }

        .habit-item-edit-form .save-habit-btn {
            background-color: #28a745;
        }

        .habit-item-edit-form .cancel-habit-btn {
            background-color: #6c757d;
        }

        #widget-column {
            gap: 20px;
            overflow: visible !important;
        }

        .widget {
            flex: 1;
            min-height: 180px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            padding: 20px 15px 35px 15px;
            transition: flex 0.3s ease-in-out;
        }

        .widget button.icon-btn {
            font-size: 40px;
            background: none;
            border: none;
            padding: 10px;
            line-height: 1;
            margin-top: 0;
        }

        .widget button.icon-btn:hover {
            background-color: #e9ecef;
        }

        #widget-status:has(#status-trigger-btn:not([style*="display: none"])) {
            justify-content: center;
        }

        #widget-status:has(#status-content:not([style*="display: none"])) {
            justify-content: flex-start;
        }

        #status-trigger-btn {
            width: 80%;
            max-width: 220px;
            margin-top: 0;
            flex-shrink: 0;
        }

        #status-trigger-btn:hover {
            background-color: #357abd;
        }

        #status-content {
            width: 100%;
            display: none;
            flex-direction: column;
            align-items: center;
        }

        #status-message {
            display: flex;
            justify-content: center;
            text-align: center;
            padding: 5px 5px 10px 5px;
            /* Reduced top and bottom padding */
            width: 100%;
            min-height: 40px;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
            /* Reduced bottom margin */
            white-space: pre-line;
            /* To respect newlines in quotes */
        }

        #status-message p {
            font-size: 16px;
            color: #333;
            font-style: italic;
            margin: 0;
        }

        #status-options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            align-items: center;
            width: 100%;
        }

        #status-options.eight-states button {
            flex-basis: calc(25% - 8px);
            /* For 8 buttons, smaller gap */
            min-width: 60px;
            font-size: 13px;
            padding: 8px 5px;
        }

        #status-options button {
            background-color: #6c757d;
            min-width: 80px;
            flex-basis: calc(50% - 10px);
            /* Default for 4 buttons */
            margin-top: 0;
        }

        #status-options button:hover {
            background-color: #5a6268;
        }

        #status-options button.active {
            background-color: #4a90e2;
            font-weight: bold;
        }

        #status-settings .status-setting-group {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px dashed #ccc;
        }

        #status-settings .status-setting-group:last-child {
            border-bottom: none;
            margin-bottom: 5px;
        }

        #status-settings label {
            font-size: 13px;
            margin-bottom: 3px;
        }

        #status-settings input[type="text"],
        #status-settings input[type="checkbox"] {
            font-size: 13px;
            padding: 5px 8px;
            width: 100%;
        }

        #status-settings input[type="checkbox"] {
            width: auto;
            margin-left: 5px;
            vertical-align: middle;
        }

        #status-settings .extra-status-setting {
            display: none;
        }

        /* Initially hide extra settings */
        #status-settings .status-quotes-inputs .multi-quote-extra {
            margin-top: 5px;
            /* Space for additional quote inputs */
        }

        #status-settings .status-quotes-inputs .multi-quote-extra label {
            font-weight: normal;
            color: #555;
        }


        /* --- Modals General Styles (Keep Existing) --- */
        .modal {
            display: none;
            position: fixed;
            z-index: 100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: #fefefe;
            margin: auto;
            padding: 25px;
            border: 1px solid #888;
            width: 90%;
            max-width: 500px;
            border-radius: 8px;
            position: relative;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
        }

        .modal-close-btn {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0 5px;
            margin: 0;
            line-height: 1;
        }

        .modal-close-btn:hover,
        .modal-close-btn:focus {
            color: black;
            text-decoration: none;
        }

        .modal-body {
            overflow-y: auto;
            margin-bottom: 15px;
        }

        /* --- Level Manager Modal Styles (Keep Existing) --- */
        .level-manage-item {
            display: flex;
            flex-direction: column;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .level-manage-item:last-child {
            border-bottom: none;
        }

        .level-manage-item-display {
            display: flex;
            align-items: center;
            width: 100%;
        }

        .level-manage-item-display .level-indicator {
            flex-shrink: 0;
        }

        .level-manage-item-display .level-text {
            flex-grow: 1;
            margin: 0 10px;
            font-size: 15px;
        }

        .level-manage-item-display .level-manage-controls {
            display: flex;
            gap: 5px;
            flex-shrink: 0;
        }

        .level-manage-item-display .level-manage-controls button {
            font-size: 11px;
            padding: 3px 6px;
            margin: 0;
            background-color: #adb5bd;
        }

        .level-manage-item-display .level-manage-controls button.edit-level-btn {
            background-color: #ffc107;
            color: #333;
        }

        /* NEW: Style for Aux Info button */
        .level-manage-item-display .level-manage-controls button.aux-info-btn {
            background-color: #17a2b8;
        }

        .level-manage-item-display .level-manage-controls button.delete-level-btn {
            background-color: #dc3545;
        }

        .level-manage-item-edit {
            display: none;
            align-items: center;
            width: 100%;
            margin-top: 5px;
        }

        .level-manage-item-edit .level-indicator {
            flex-shrink: 0;
        }

        .level-manage-item-edit .level-text-edit {
            flex-grow: 1;
            margin: 0 10px;
        }

        .level-manage-item-edit .level-text-edit input[type="text"] {
            font-size: 14px;
            padding: 5px 8px;
            width: 100%;
            margin: 0;
        }

        .level-manage-item-edit .level-manage-controls {
            display: flex;
            gap: 5px;
            flex-shrink: 0;
        }

        .level-manage-item-edit .level-manage-controls button {
            font-size: 11px;
            padding: 3px 6px;
            margin: 0;
        }

        .level-manage-item-edit .level-manage-controls button.save-level-btn {
            background-color: #28a745;
        }

        .level-manage-item-edit .level-manage-controls button.cancel-level-btn {
            background-color: #6c757d;
        }

        .add-level-section {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .add-level-section input {
            margin-bottom: 5px;
        }

        .add-level-section button {
            width: 100%;
            font-size: 14px;
            padding: 8px;
            margin: 0;
        }

        #modal-delete-task-btn {
            background-color: #dc3545;
            margin-top: 15px;
            width: 100%;
            font-size: 14px;
            padding: 8px;
        }


        /* --- Import/Export Styles (Keep Existing) --- */
        #import-modal .modal-body {
            padding: 15px 0;
        }

        #import-modal .import-checkbox-container {
            margin-bottom: 15px;
        }

        #import-modal .import-checkbox-container label {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        #import-modal .import-checkbox-container input[type="checkbox"] {
            margin-right: 10px;
            width: auto;
        }

        #import-modal .import-warning {
            color: #dc3545;
            margin-top: 15px;
            font-size: 14px;
            font-style: italic;
        }

        #import-modal .import-file-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        #import-modal .import-file-info p {
            margin-bottom: 5px;
        }

        #import-modal .import-file-info .file-name {
            font-weight: bold;
        }

        #import-modal .import-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 15px;
        }

        #import-modal .import-actions button {
            margin: 0;
            padding: 8px 15px;
        }

        #import-modal #confirm-import-btn {
            background-color: #28a745;
        }

        #import-modal #cancel-import-btn {
            background-color: #6c757d;
        }

        /* --- Page 3 Styles (Keep Existing Grid, update placeholder-bl) --- */
        #back-to-page2-btn {
            position: absolute;
            bottom: 15px;
            left: 15px;
            background: none;
            border: 1px solid #aaa;
            color: #777;
            font-size: 20px;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            line-height: 30px;
            text-align: center;
            cursor: pointer;
            padding: 0;
            margin: 0;
            z-index: 20;
        }

        .page3-tools {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            z-index: 20;
            margin: 0;
        }

        .page3-tools button {
            background: none;
            border: 1px solid #aaa;
            color: #777;
            font-size: 14px;
            border-radius: 4px;
            padding: 3px 8px;
            margin: 0;
            margin-top: 0 !important;
            cursor: pointer;
        }

        .page3-tools button:hover {
            background-color: #eee;
            color: #333;
            border-color: #888;
        }

        #back-to-page2-btn:hover {
            background-color: #eee;
            color: #333;
            border-color: #888;
        }

        #page3 .page3-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 2fr 1fr;
            width: 100%;
            height: 100%;
            max-width: 1300px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 20px;
            gap: 20px;
            position: relative;
        }

        .quadrant {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        #table-module {
            grid-column: 1 / 2;
            grid-row: 1 / 2;
        }

        #qa-module {
            grid-column: 2 / 3;
            grid-row: 1 / 2;
        }

        #placeholder-bl {
            grid-column: 1 / 2;
            grid-row: 2 / 3;
            padding: 10px;
        }

        #placeholder-br {
            grid-column: 2 / 3;
            grid-row: 2 / 3;
        }

        .placeholder-quadrant {
            display: flex;
            justify-content: center;
            align-items: center;
            color: #ccc;
            font-size: 24px;
            font-style: italic;
        }

        /* Styles for the Today module (bottom-right) */
        .std-btn {
            padding: 8px 15px;
            font-size: 14px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #5a6268;
            color: white;
            transition: background-color 0.2s ease, transform 0.1s ease;
            margin-top: 5px;
        }

        .std-btn:hover:not(:disabled) {
            background-color: #495057;
        }

        .std-btn:active:not(:disabled) {
            transform: scale(0.98);
        }

        .std-btn:disabled {
            background-color: #c0c0c0;
            cursor: not-allowed;
        }

        #today-left-panel .rating-btn {
            min-width: 35px;
            padding: 6px 8px;
            font-size: 13px;
        }

        #today-left-panel .rating-btn.active {
            background-color: #4a90e2;
            font-weight: bold;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        #today-right-streak-panel .std-btn {
            padding: 8px 12px;
            font-size: 13px;
            width: 100px;
            text-align: center;
            margin-top: 0;
        }

        #streak-task-name-display.clickable {
            cursor: pointer;
            text-decoration: underline;
        }

        #streak-task-name-display.clickable:hover {
            color: #4a90e2;
        }

        #placeholder-br.quadrant {
            padding: 0;
            display: flex;
            justify-content: initial;
            align-items: initial;
        }

        #today-module-container {
            display: flex;
            width: 100%;
            height: 100%;
            justify-content: space-between;
        }

        #today-left-panel,
        #today-right-streak-panel {
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 10px 5px 10px;
            justify-content: space-between;
        }

        #today-left-panel {
            flex: 3;
            border-right: 1px solid #e0e0e0;
        }

        #today-right-streak-panel {
            flex: 2;
        }

        #today-left-panel h4,
        #today-right-streak-panel h4 {
            margin-bottom: 5px;
            font-size: 18px;
            color: #555;
            text-align: center;
            width: 100%;
        }

        /* NEW: Today title clickable */
        #today-title {
            cursor: pointer;
        }

        #today-current-rating {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .today-rating-buttons {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }

        .today-mood-counters {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            width: 100%;
            justify-content: center;
        }

        .mood-counter-item {
            text-align: center;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0 6px;
        }

        .mood-counter-item .mood-count-text {
            font-size: 11px;
            display: block;
            user-select: none;
            margin-bottom: 2px;
            transition: transform 0.2s ease-out;
        }

        .mood-counter-item .mood-count-text.animate-mood {
            transform: scale(1.4);
        }

        .mood-counter-item .mood-icon {
            font-size: 18px;
            user-select: none;
            line-height: 1;
        }

        #streak-count-display {
            font-size: 26px;
            font-weight: bold;
            margin-bottom: 3px;
            color: #333;
        }

        #streak-task-name-display {
            font-size: 13px;
            color: #666;
            margin-bottom: 8px;
            height: 18px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 90%;
            text-align: center;
            cursor: default;
        }

        #streak-action-buttons-container {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: auto;
            min-height: 35px;
        }

        #streak-protection-warning {
            font-size: 10px;
            color: #777;
            margin-bottom: 3px;
            text-align: center;
            display: none;
        }

        #today-right-streak-panel .std-btn.streak-protect-active {
            background-color: #4a90e2;
        }

        #today-right-streak-panel .std-btn.streak-protect-active:hover:not(:disabled) {
            background-color: #357abd;
        }

        #streak-protection-points-display {
            font-size: 10px;
            color: #777;
            margin-top: 5px;
            height: 13px;
        }

        #challenge-todo-select,
        #add-challenge-modal input[type="text"],
        #add-challenge-modal textarea {
            background-color: white;
            font-size: 14px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 100%;
            margin-bottom: 10px;
        }

        #add-challenge-modal .modal-content,
        #streak-task-details-modal .modal-content {
            max-height: 90vh;
        }

        #streak-task-details-modal .modal-body p {
            margin-bottom: 8px;
        }


        /* --- Records Module (Bottom-Left) Styles --- */
        #records-module-container {
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 100%;
        }

        #records-module-container h4 {
            text-align: center;
            color: #444;
            margin-bottom: 10px;
            font-size: 16px;
            flex-shrink: 0;
        }

        .records-controls {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-bottom: 10px;
            flex-shrink: 0;
        }

        .records-controls .std-btn {
            padding: 5px 10px;
            font-size: 12px;
            margin-top: 0;
        }

        .records-controls .std-btn.active {
            background-color: #4a90e2;
            font-weight: bold;
        }

        #records-chart-area {
            flex-grow: 1;
            border: 1px solid #e0e0e0;
            background-color: #fff;
            border-radius: 4px;
            padding: 10px 5px 20px 5px;
            /* Increased bottom padding for day of week */
            overflow-x: auto;
            overflow-y: hidden;
            display: flex;
            align-items: flex-end;
            gap: 4px;
            position: relative;
        }

        /* NEW: Month Navigation buttons */
        .month-nav-btn {
            position: absolute;
            top: 2px;
            background: rgba(255, 255, 255, 0.7);
            border: 1px solid #ccc;
            color: #555;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            line-height: 26px;
            font-size: 16px;
            padding: 0;
            cursor: pointer;
            z-index: 5;
            display: none;
            /* Hidden by default */
        }

        .month-nav-btn:hover:not(:disabled) {
            background: #eee;
        }

        .month-nav-btn:disabled {
            background: #f5f5f5;
            color: #ccc;
            cursor: not-allowed;
        }

        #records-prev-month-btn {
            left: 5px;
        }

        #records-next-month-btn {
            right: 5px;
        }


        #records-chart-area .placeholder-msg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            text-align: center;
            display: none;
        }

        .records-chart-day-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 35px;
            height: 100%;
            position: relative;
        }

        .records-chart-day-bars {
            display: flex;
            align-items: flex-end;
            justify-content: center;
            gap: 2px;
            height: calc(100% - 30px);
            /* Adjusted for date + day of week */
            width: 100%;
        }

        .records-chart-bar {
            width: 10px;
            background-color: #ccc;
            border-radius: 2px 2px 0 0;
            transition: height 0.3s ease-out;
            position: relative;
        }

        .records-chart-bar span {
            font-size: 9px;
            color: #333;
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            white-space: nowrap;
        }

        .records-chart-bar.tasks {
            background-color: #28a745;
        }

        .records-chart-bar.habits {
            background-color: #ffc107;
        }

        .records-chart-bar.rating-positive {
            background-color: #4a90e2;
        }

        .records-chart-bar.rating-negative {
            background-color: #dc3545;
        }

        .records-chart-bar.rating-zero {
            background-color: #f0f0f0;
            border: 1px solid #ccc;
        }

        .records-chart-date-label {
            font-size: 10px;
            color: #555;
            margin-top: 5px;
            text-align: center;
            width: 100%;
            white-space: nowrap;
            position: absolute;
            bottom: 10px;
            /* Position for M/D */
            left: 0;
        }

        .records-chart-day-label {
            font-size: 9px;
            color: #777;
            text-align: center;
            width: 100%;
            white-space: nowrap;
            position: absolute;
            bottom: 2px;
            /* Position for Day of Week */
            left: 0;
        }


        /* Table Module Specific Styles (Keep Existing) */
        #table-module .table-controls {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
            flex-shrink: 0;
            gap: 10px;
        }

        #table-module .table-tabs-container {
            display: flex;
            gap: 5px;
            flex-grow: 1;
            min-width: 200px;
            overflow-x: auto;
            padding-bottom: 5px;
        }

        #table-module .table-tab {
            background-color: #e9ecef;
            color: #495057;
            border: 1px solid #dee2e6;
            border-bottom: none;
            padding: 8px 12px;
            font-size: 14px;
            border-radius: 4px 4px 0 0;
            cursor: pointer;
            white-space: nowrap;
        }

        #table-module .table-tab:hover {
            background-color: #f8f9fa;
        }

        #table-module .table-tab.active {
            background-color: #fff;
            border-color: #dee2e6;
            border-bottom: 1px solid #fff;
            position: relative;
            top: 1px;
            font-weight: bold;
        }

        #table-module .table-name-editor {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        #table-module .table-name-editor label {
            font-size: 14px;
            color: #666;
            margin-bottom: 0;
        }

        #table-module #table-name-input {
            padding: 6px 8px;
            font-size: 14px;
            border-radius: 3px;
            border: 1px solid #ccc;
            width: auto;
            min-width: 120px;
            flex-grow: 1;
            margin-bottom: 0;
        }

        #table-module .table-action-buttons {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        #table-module .table-action-buttons button {
            padding: 6px 10px;
            font-size: 13px;
            margin-top: 0;
            background-color: #6c757d;
        }

        #table-module .table-action-buttons button:hover:not(:disabled) {
            background-color: #5a6268;
        }

        #table-module #table-clear-btn {
            background-color: #dc3545;
        }

        #table-module #table-clear-btn:hover:not(:disabled) {
            background-color: #c82333;
        }

        #table-module .table-render-area {
            flex-grow: 1;
            overflow: auto;
            border: 1px solid #dee2e6;
            border-radius: 0 0 4px 4px;
        }

        #table-module table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        #table-module th,
        #table-module td {
            border: 1px solid #dee2e6;
            padding: 8px 10px;
            font-size: 14px;
            text-align: left;
            vertical-align: top;
            word-wrap: break-word;
            white-space: normal;
            outline: none;
        }

        #table-module th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: relative;
            white-space: nowrap;
        }

        #table-module td {
            background-color: #fff;
            min-height: 30px;
        }

        #table-module th:focus,
        #table-module td:focus {
            background-color: #eef5ff;
            box-shadow: inset 0 0 0 1px #4a90e2;
        }

        #table-module thead th {
            position: sticky;
            top: 0;
            z-index: 1;
            background-color: #f8f9fa;
        }

        #table-module th .resize-handle {
            position: absolute;
            top: 0;
            right: -3px;
            bottom: 0;
            width: 6px;
            cursor: col-resize;
            z-index: 2;
        }

        /* --- Q&A Module Specific Styles (MODIFIED) --- */
        #qa-module .qa-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
            flex-shrink: 0;
        }

        #qa-module .qa-controls button {
            padding: 6px 10px;
            font-size: 13px;
            margin-top: 0;
            background-color: #6c757d;
        }

        #qa-module .qa-controls button:hover:not(:disabled) {
            background-color: #5a6268;
        }

        #qa-module #qa-clear-answers-btn {
            background-color: #ffc107;
            color: #333;
        }

        #qa-module #qa-clear-answers-btn:hover:not(:disabled) {
            background-color: #e0a800;
        }

        #qa-module .qa-content-area {
            flex-grow: 1;
            overflow-y: auto;
            padding: 5px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .qa-pair {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .qa-question {
            align-self: flex-start;
            background-color: #e9ecef;
            color: #343a40;
            padding: 8px 12px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
            border: 1px solid #dee2e6;
            width: fit-content;
        }

        .qa-answer {
            align-self: flex-end;
            background-color: #cfe2ff;
            color: #004085;
            padding: 8px 12px;
            border-radius: 15px;
            max-width: 80%;
            /* Controls max width of the bubble */
            border: 1px solid #b8daff;
            font-size: 14px;
            line-height: 1.5;
            word-wrap: break-word;
            /* Ensures text wraps within the bubble */
            white-space: pre-line;
            /* ADDED: Respects newlines in text content */
            min-height: 20px;
            /* Minimum height for visual presence */
            width: fit-content;
            /* Bubble width fits content up to max-width */
            cursor: pointer;
            /* Indicates it's clickable to edit/add answer */
        }

        .qa-answer:empty::before {
            /* Placeholder text for empty answers */
            content: "点击添加回答...";
            font-style: italic;
            color: #6c757d;
        }

        #qa-answer-modal-input {
            /* Textarea within the answer modal */
            width: 100%;
            padding: 10px;
            font-size: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-bottom: 10px;
            /* From general textarea, good to keep */
            line-height: 1.5;
            box-sizing: border-box;
        }

        #qa-module .qa-folded-placeholder {
            flex-grow: 1;
            display: none;
            justify-content: center;
            align-items: center;
            color: #ccc;
            font-size: 24px;
            font-style: italic;
        }

        /* QA Editor Modal Specific Styles (Keep Existing) */
        #qa-editor-modal .modal-body {
            padding-top: 5px;
        }

        /* Todo Focus Mode Styles */
        .todo-focus-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 150;
            display: none;
            pointer-events: auto;
        }

        .todo-focus-overlay.active {
            display: block;
        }

        .todo-item.focused {
            position: relative;
            z-index: 151;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }

        /* NEW: Styles for auxiliary info display in focus mode */
        #focus-aux-info {
            position: fixed;
            max-width: 270px;
            z-index: 152;
            background: rgba(20, 20, 20, 0.75);
            color: #e0e0e0;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            pointer-events: none;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .qa-edit-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 5px;
            border-bottom: 1px solid #f0f0f0;
        }

        .qa-edit-item:last-child {
            border-bottom: none;
        }

        .qa-edit-item-text {
            flex-grow: 1;
            margin-right: 15px;
            font-size: 15px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .qa-edit-item-controls {
            display: flex;
            gap: 8px;
            flex-shrink: 0;
        }

        .qa-edit-item-controls button {
            font-size: 12px;
            padding: 4px 8px;
            margin: 0;
            background-color: #adb5bd;
        }

        .qa-edit-item-controls button.edit-qa-btn {
            background-color: #ffc107;
            color: #333;
        }

        .qa-edit-item-controls button.delete-qa-btn {
            background-color: #dc3545;
        }

        /* NEW: Habit Adjustment Modal Styles */
        .storage-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            margin: 5px 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .storage-item:hover {
            background-color: #f0f0f0;
        }

        .delete-stored-habit-btn {
            color: #dc3545;
            font-weight: bold;
            font-size: 20px;
            line-height: 1;
            padding: 0 5px;
            cursor: pointer;
        }

        .delete-stored-habit-btn:hover {
            color: #a51e2b;
        }

        /* NEW: Mood Customization Modal Styles */
        #mood-customization-modal .modal-body {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        #mood-customization-modal .mood-input-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        #mood-customization-modal .mood-input-group label {
            width: 80px;
        }

        #mood-customization-modal .mood-input-group input {
            flex-grow: 1;
            font-size: 18px;
            text-align: center;
            padding: 5px;
            margin-bottom: 0;
        }

        /* 在原有样式后追加如下内容，隐藏滚动条但保留滚动功能 */
        #todo-column,
        #habit-column,
        #widget-column {
            scrollbar-width: none;
            /* Firefox */
            -ms-overflow-style: none;
            /* IE 10+ */
        }

        #todo-column::-webkit-scrollbar,
        #habit-column::-webkit-scrollbar,
        #widget-column::-webkit-scrollbar {
            display: none;
            /* Chrome/Safari/Edge */
        }
    </style>
</head>

<body>

    <div id="page1" class="page">
        <div class="content-box">
            <h1>学习启动仪表盘</h1>
            <p class="subtitle">你的努力，即使再小，也值得庆祝</p>
            <h3>今天，我们只做一件小事：</h3>
            <input type="text" id="p1-todo-input" placeholder="打开记录本随意看几下">
            <button id="p1-complete-btn">我做完了!</button>
            <div id="p1-celebration" class="feedback-animation">🎉</div>
        </div>
        <div class="data-tools">
            <button id="export-data-btn" title="导出数据">💾 导出</button>
            <button id="import-data-btn" title="导入数据">📂 导入</button>
            <button id="hide-intro-btn" title="隐藏导向页">🔒 隐藏导向页</button>
            <input type="file" id="import-file-input" accept=".json">
        </div>
        <button id="next-page-btn" title="进入主面板">➔</button>
    </div>

    <div id="page2" class="page">
        <button id="back-to-page1-btn" title="返回第一页">←</button>
        <div class="page2-container">
            <div id="todo-column" class="column">
                <h2>任务列表</h2>
                <div id="todo-actions">
                    <button id="reset-todos-btn">☀️ 新重置/安排</button> <!-- Text changed -->
                    <button id="clear-todos-btn">🗑️ 清空/拉窗帘</button>
                </div>
                <div id="p2-todo-celebration" class="feedback-animation">🎉</div>
                <div class="list-container">
                    <div id="todo-congrats-any-lv1" class="congrats-msg placeholder-msg">不错👍</div>
                    <div id="todo-congrats-lv1" class="congrats-msg placeholder-msg">📏 所有基础都做完了！👍</div>
                    <div id="todo-congrats-all" class="congrats-msg placeholder-msg">🎉 恭喜！所有任务已完成！</div>
                    <p id="no-tasks-msg" class="placeholder-msg">快添加一个任务吧！只需要先添加重要但简单的小事：）</p>
                    <ul id="todo-list"></ul>
                </div>
                <div class="input-area"> <input type="text" id="new-todo-input" placeholder="添加新任务 (Lv1)..."> <button
                        id="add-todo-btn">添加任务</button> </div>
            </div>
            <div id="habit-column" class="column">
                <h2>习惯养成</h2>
                <div id="habit-celebration" class="feedback-animation">✨</div>
                <div id="habit-sadness" class="feedback-animation">😢</div>
                <div class="list-container">
                    <p id="no-habits-msg" class="placeholder-msg">添加你的第一个习惯吧！</p>
                    <ul id="habit-list"></ul>
                </div>
                <div class="input-area"> <input type="text" id="new-habit-title" placeholder="习惯标题 (例如: 快速整理)">
                    <textarea id="new-habit-desc" placeholder="简短描述 (可选)"></textarea> <button
                        id="add-habit-btn">添加习惯</button>
                </div>
            </div>
            <div id="widget-column" class="column">
                <div id="widget-url1" class="widget"> <button id="url-btn1" class="icon-btn" title="打开链接1">🔗</button>
                    <div class="widget-controls"> <span class="settings-icon"
                            onclick="toggleSettings('url1-settings')">⚙️</span> </div>
                    <div id="url1-settings" class="settings-panel"> <label for="url1-input">URL:</label> <input
                            type="url" id="url1-input"> <label for="emoji1-input">图标:</label> <input type="text"
                            id="emoji1-input" placeholder="🔗" maxlength="2"> <button
                            onclick="saveUrlSettings(1)">保存</button> <button type="button"
                            onclick="toggleSettings('url1-settings')">取消</button> </div>
                </div>
                <div id="widget-url2" class="widget"> <button id="url-btn2" class="icon-btn" title="打开链接2">⛳</button>
                    <div class="widget-controls"> <span class="settings-icon"
                            onclick="toggleSettings('url2-settings')">⚙️</span> </div>
                    <div id="url2-settings" class="settings-panel"> <label for="url2-input">URL:</label> <input
                            type="url" id="url2-input"> <label for="emoji2-input">图标:</label> <input type="text"
                            id="emoji2-input" placeholder="⛳" maxlength="2"> <button
                            onclick="saveUrlSettings(2)">保存</button> <button type="button"
                            onclick="toggleSettings('url2-settings')">取消</button> </div>
                </div>
                <div id="widget-status" class="widget"> <button id="status-trigger-btn">我当前的状态...</button>
                    <div id="status-content">

                        <div id="status-message">
                            <p id="status-quote">&nbsp;</p>
                        </div>
                        <div id="status-options"> <button id="status-option1" data-index="0">状态1</button> <button
                                id="status-option2" data-index="1">状态2</button> <button id="status-option3"
                                data-index="2">状态3</button> <button id="status-option4" data-index="3">状态4</button>
                            <button id="status-option5" data-index="4" style="display:none;">状态5</button> <button
                                id="status-option6" data-index="5" style="display:none;">状态6</button> <button
                                id="status-option7" data-index="6" style="display:none;">状态7</button> <button
                                id="status-option8" data-index="7" style="display:none;">状态8</button>
                        </div>
                    </div>
                    <div class="widget-controls"> <span id="status-reset-btn" class="reset-icon"
                            title="重置状态显示">🔄</span> <span class="settings-icon"
                            onclick="toggleSettings('status-settings')">⚙️</span> </div>
                    <div id="status-settings" class="settings-panel">
                        <h4>状态设置</h4>
                        <div style="margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #ccc;">
                            <label for="status-mode-toggle"
                                style="display:inline-block; margin-right: 5px; font-weight:normal;">启用8状态模式:</label>
                            <input type="checkbox" id="status-mode-toggle"
                                onchange="handleStatusModeToggle(this.checked)">
                        </div>
                        <div style="margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #ccc;">
                            <label for="status-multi-select-toggle"
                                style="display:inline-block; margin-right: 5px; font-weight:normal;">允许至多3状态选择:</label>
                            <input type="checkbox" id="status-multi-select-toggle"
                                onchange="handleMultiStatusToggle(this.checked)">
                        </div>
                        <div style="margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #ccc;">
                            <label for="status-multi-quote-toggle"
                                style="display:inline-block; margin-right: 5px; font-weight:normal;">启用多鼓励语模式
                                (随机显示):</label>
                            <input type="checkbox" id="status-multi-quote-toggle"
                                onchange="handleMultiQuoteModeToggle(this.checked)">
                        </div>

                        <div class="status-setting-group">
                            <label for="status1-name">状态 1:</label> <input type="text" id="status1-name">
                            <div class="status-quotes-inputs">
                                <label for="status1-quote1">鼓励语 1-1:</label> <input type="text" id="status1-quote1">
                                <div class="multi-quote-extra" style="display:none;">
                                    <label for="status1-quote2">鼓励语 1-2:</label> <input type="text" id="status1-quote2">
                                    <label for="status1-quote3">鼓励语 1-3:</label> <input type="text" id="status1-quote3">
                                </div>
                            </div>
                        </div>
                        <div class="status-setting-group">
                            <label for="status2-name">状态 2:</label> <input type="text" id="status2-name">
                            <div class="status-quotes-inputs">
                                <label for="status2-quote1">鼓励语 2-1:</label> <input type="text" id="status2-quote1">
                                <div class="multi-quote-extra" style="display:none;">
                                    <label for="status2-quote2">鼓励语 2-2:</label> <input type="text" id="status2-quote2">
                                    <label for="status2-quote3">鼓励语 2-3:</label> <input type="text" id="status2-quote3">
                                </div>
                            </div>
                        </div>
                        <div class="status-setting-group">
                            <label for="status3-name">状态 3:</label> <input type="text" id="status3-name">
                            <div class="status-quotes-inputs">
                                <label for="status3-quote1">鼓励语 3-1:</label> <input type="text" id="status3-quote1">
                                <div class="multi-quote-extra" style="display:none;">
                                    <label for="status3-quote2">鼓励语 3-2:</label> <input type="text" id="status3-quote2">
                                    <label for="status3-quote3">鼓励语 3-3:</label> <input type="text" id="status3-quote3">
                                </div>
                            </div>
                        </div>
                        <div class="status-setting-group">
                            <label for="status4-name">状态 4:</label> <input type="text" id="status4-name">
                            <div class="status-quotes-inputs">
                                <label for="status4-quote1">鼓励语 4-1:</label> <input type="text" id="status4-quote1">
                                <div class="multi-quote-extra" style="display:none;">
                                    <label for="status4-quote2">鼓励语 4-2:</label> <input type="text" id="status4-quote2">
                                    <label for="status4-quote3">鼓励语 4-3:</label> <input type="text" id="status4-quote3">
                                </div>
                            </div>
                        </div>
                        <div class="status-setting-group extra-status-setting">
                            <label for="status5-name">状态 5:</label> <input type="text" id="status5-name">
                            <div class="status-quotes-inputs">
                                <label for="status5-quote1">鼓励语 5-1:</label> <input type="text" id="status5-quote1">
                                <div class="multi-quote-extra" style="display:none;">
                                    <label for="status5-quote2">鼓励语 5-2:</label> <input type="text" id="status5-quote2">
                                    <label for="status5-quote3">鼓励语 5-3:</label> <input type="text" id="status5-quote3">
                                </div>
                            </div>
                        </div>
                        <div class="status-setting-group extra-status-setting">
                            <label for="status6-name">状态 6:</label> <input type="text" id="status6-name">
                            <div class="status-quotes-inputs">
                                <label for="status6-quote1">鼓励语 6-1:</label> <input type="text" id="status6-quote1">
                                <div class="multi-quote-extra" style="display:none;">
                                    <label for="status6-quote2">鼓励语 6-2:</label> <input type="text" id="status6-quote2">
                                    <label for="status6-quote3">鼓励语 6-3:</label> <input type="text" id="status6-quote3">
                                </div>
                            </div>
                        </div>
                        <div class="status-setting-group extra-status-setting">
                            <label for="status7-name">状态 7:</label> <input type="text" id="status7-name">
                            <div class="status-quotes-inputs">
                                <label for="status7-quote1">鼓励语 7-1:</label> <input type="text" id="status7-quote1">
                                <div class="multi-quote-extra" style="display:none;">
                                    <label for="status7-quote2">鼓励语 7-2:</label> <input type="text" id="status7-quote2">
                                    <label for="status7-quote3">鼓励语 7-3:</label> <input type="text" id="status7-quote3">
                                </div>
                            </div>
                        </div>
                        <div class="status-setting-group extra-status-setting">
                            <label for="status8-name">状态 8:</label> <input type="text" id="status8-name">
                            <div class="status-quotes-inputs">
                                <label for="status8-quote1">鼓励语 8-1:</label> <input type="text" id="status8-quote1">
                                <div class="multi-quote-extra" style="display:none;">
                                    <label for="status8-quote2">鼓励语 8-2:</label> <input type="text" id="status8-quote2">
                                    <label for="status8-quote3">鼓励语 8-3:</label> <input type="text" id="status8-quote3">
                                </div>
                            </div>
                        </div>
                        <button onclick="saveStatusSettings()">保存</button> <button type="button"
                            onclick="toggleSettings('status-settings')">取消</button>
                    </div>
                </div>
            </div>
        </div>
        <button id="next-page3-btn" title="进入扩展面板">➔</button>
    </div>

    <div id="page3" class="page">
        <button id="back-to-page2-btn" title="返回主面板">←</button>
        <div id="page3-tools" class="page3-tools" style="display: none;">
            <button id="habit-adjustment-btn" title="习惯调整"> 习惯调整</button>
            <button id="export-data-p3-btn" title="导出数据">💾 导出</button>
            <button id="import-data-p3-btn" title="导入数据">📂 导入</button>

            <button id="show-intro-btn" title="恢复导向页">🔓 导向页</button>
            <button id="simplify-mode-btn" title="简化添加框">📝 再简化</button>

        </div>
        <div class="page3-container">
            <div id="table-module" class="quadrant">
                <div class="table-controls">
                    <div class="table-tabs-container" id="table-tabs"></div>
                    <div class="table-name-editor">
                        <label for="table-name-input">表名:</label>
                        <input type="text" id="table-name-input" placeholder="当前表格名称">
                    </div>
                    <div class="table-action-buttons">
                        <button id="table-add-col-btn">添加列</button>
                        <button id="table-add-row-btn">添加行</button>
                        <button id="table-del-col-btn">删除列</button>
                        <button id="table-del-row-btn">删除行</button>
                        <button id="table-clear-btn">清空本页</button>
                    </div>
                </div>
                <div class="table-render-area" id="table-render-area"></div>
            </div>
            <div id="qa-module" class="quadrant">
                <div class="qa-controls">
                    <button id="qa-add-btn">添加提问</button>
                    <button id="qa-edit-btn">编辑提问</button>
                    <button id="qa-clear-answers-btn">清空回答</button>
                    <button id="qa-groups-btn">问题组</button>
                    <button id="qa-toggle-fold-btn">折叠</button>
                </div>
                <div class="qa-content-area" id="qa-content-area"></div>
                <div class="qa-folded-placeholder placeholder-msg" id="qa-folded-placeholder">提问回答</div>
            </div>
            <div id="placeholder-bl" class="quadrant">
                <div id="records-module-container">
                    <h4>数据</h4>
                    <div class="records-controls">
                        <button id="record-view-week" class="std-btn active">周视图</button>
                        <button id="record-view-month" class="std-btn">月视图</button>
                        <button id="clear-records-btn" class="std-btn" style="background-color: #dc3545;">清空记录</button>
                    </div>
                    <div id="records-chart-area">
                        <button id="records-prev-month-btn" class="month-nav-btn">←</button>
                        <button id="records-next-month-btn" class="month-nav-btn">→</button>
                        <p class="placeholder-msg" style="display: block;">暂无记录数据</p>
                        <!-- Chart will be rendered here by JS -->
                    </div>
                </div>
            </div>
            <div id="placeholder-br" class="quadrant">
                <div id="today-module-container">
                    <div id="today-left-panel">
                        <h4 id="today-title">Today</h4>
                        <div id="today-current-rating">+0</div>
                        <div class="today-rating-buttons">
                            <button data-value="2" class="std-btn rating-btn">+2</button>
                            <button data-value="1" class="std-btn rating-btn">+1</button>
                            <button data-value="0" class="std-btn rating-btn active">0</button>
                            <button data-value="-1" class="std-btn rating-btn">-1</button>
                            <button data-value="-2" class="std-btn rating-btn">-2</button>
                        </div>
                        <div class="today-mood-counters">
                            <div class="mood-counter-item">
                                <span id="mood-angry-count" class="mood-count-text">x0</span>
                                <span id="mood-angry-icon" class="mood-icon" data-mood="angry">😡</span>
                            </div>
                            <div class="mood-counter-item">
                                <span id="mood-neutral-count" class="mood-count-text">x0</span>
                                <span id="mood-neutral-icon" class="mood-icon" data-mood="neutral">😑</span>
                            </div>
                            <div class="mood-counter-item">
                                <span id="mood-hurt-count" class="mood-count-text">x0</span>
                                <span id="mood-hurt-icon" class="mood-icon" data-mood="hurt">🤕</span>
                            </div>
                        </div>
                    </div>
                    <div id="today-right-streak-panel">
                        <h4>Streak</h4>
                        <div id="streak-count-display">0</div>
                        <div id="streak-task-name-display" title="No task selected">未开始</div>
                        <div id="streak-action-buttons-container">
                            <div id="streak-protection-warning"></div>
                            <button id="streak-add-challenge-btn" class="std-btn">添加挑战</button>
                            <button id="streak-done-btn" class="std-btn" style="display:none;">Done</button>
                            <button id="streak-protect-btn" class="std-btn" style="display:none;">保护</button>
                            <button id="streak-ended-btn" class="std-btn" style="display:none;">已结束</button>
                        </div>
                        <div id="streak-protection-points-display">保护点: 2/2</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div id="level-manager-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-task-title">管理任务层级</h3> <span class="modal-close-btn"
                    onclick="closeLevelManager()">&times;</span>
            </div>
            <div class="modal-body" id="modal-levels-list"> </div>
            <div class="add-level-section" id="modal-add-level-section">
                <h4>添加新层级 (Lv<span id="modal-next-level-num"></span>)</h4> <input type="text" id="modal-new-level-text"
                    placeholder="新层级的任务描述..."> <button id="modal-add-level-btn">确认添加</button>
            </div> <button id="modal-delete-task-btn">删除此任务</button>
        </div>
    </div>
    <div id="import-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>导入数据</h3> <span class="modal-close-btn" onclick="closeImportModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="import-file-info">
                    <p>已选择文件: <span id="import-file-name" class="file-name">未选择文件</span></p>
                    <p>上次修改: <span id="import-file-date">-</span></p>
                </div>
                <div class="import-checkbox-container">
                    <h4>选择要导入的数据:</h4> <label><input type="checkbox" id="import-todos" checked> 任务列表 </label>
                    <label><input type="checkbox" id="import-habits" checked> 习惯养成</label> <label><input type="checkbox"
                            id="import-url-settings" checked> 链接设置</label> <label><input type="checkbox"
                            id="import-status-settings" checked> 状态设置 </label> <label><input type="checkbox"
                            id="import-tables" checked> 表格数据</label> <label><input type="checkbox" id="import-qa"
                            checked> 提问回答</label> <label><input type="checkbox" id="import-today-streak" checked>
                        今日与挑战数据</label> <label><input type="checkbox" id="import-daily-records" checked> 每日回顾记录</label>
                </div>
                <p class="import-warning">警告: 导入数据将会覆盖当前的数据，此操作不可撤销。</p>
            </div>
            <div class="import-actions"> <button id="cancel-import-btn" onclick="closeImportModal()">取消</button> <button
                    id="confirm-import-btn" onclick="confirmImport()">确认导入</button> </div>
        </div>
    </div>
    <div id="qa-editor-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="qa-editor-title">编辑/删除提问</h3> <span class="modal-close-btn"
                    onclick="closeQAEditorModal()">&times;</span>
            </div>
            <div class="modal-body" id="qa-editor-list"> </div>
        </div>
    </div>

    <div id="qa-answer-modal" class="modal">
        <div class="modal-content" style="max-width: 450px;">
            <div class="modal-header">
                <h3 id="qa-answer-modal-question">提问内容</h3>
                <span class="modal-close-btn" onclick="closeAnswerModal()">&times;</span>
            </div>
            <div class="modal-body">
                <label for="qa-answer-modal-input"
                    style="display: block; margin-bottom: 5px; font-weight: bold;">回答:</label>
                <textarea id="qa-answer-modal-input" rows="4" placeholder="输入您的回答..."></textarea>
            </div>
            <div style="text-align: right; padding-top:10px; border-top: 1px solid #eee; margin-top: 15px;">
                <button type="button" class="std-btn" onclick="closeAnswerModal()"
                    style="margin-right: 5px; background-color: #6c757d;">取消</button>
                <button id="qa-answer-modal-save-btn" class="std-btn" style="background-color: #28a745;">保存回答</button>
            </div>
        </div>
    </div>

    <div id="qa-groups-modal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3>问题组管理</h3>
                <span class="modal-close-btn" onclick="closeQAGroupsModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div style="display: flex; gap: 20px; height: 400px;">
                    <!-- 当前问题组列表 -->
                    <div style="flex: 1; display: flex; flex-direction: column;">
                        <h4 style="margin-bottom: 10px; text-align: center; color: #444;">已保存的问题组</h4>
                        <div id="qa-groups-list"
                            style="flex: 1; border: 1px solid #ddd; border-radius: 4px; padding: 10px; overflow-y: auto; background-color: #f9f9f9;">
                            <!-- 问题组列表将在这里显示 -->
                        </div>
                        <div style="margin-top: 10px; display: flex; gap: 5px;">
                            <input type="text" id="qa-group-name-input" placeholder="输入问题组名称..." style="flex: 1; padding: 5px;">
                            <button id="qa-save-group-btn" class="std-btn" style="background-color: #28a745;">保存当前</button>
                        </div>
                    </div>
                    <!-- 当前问题预览 -->
                    <div style="flex: 1; display: flex; flex-direction: column;">
                        <h4 style="margin-bottom: 10px; text-align: center; color: #444;">当前问题列表</h4>
                        <div id="qa-current-preview"
                            style="flex: 1; border: 1px solid #ddd; border-radius: 4px; padding: 10px; overflow-y: auto; background-color: #f0f8ff;">
                            <!-- 当前问题预览将在这里显示 -->
                        </div>
                        <p style="font-size: 12px; color: #666; text-align: center; margin-top: 5px;">左侧点击问题组可加载</p>
                    </div>
                </div>
            </div>
            <div style="text-align: center; padding-top: 10px; border-top: 1px solid #eee; margin-top: 15px;">
                <button class="std-btn" onclick="closeQAGroupsModal()" style="background-color: #6c757d;">关闭</button>
            </div>
        </div>
    </div>

    <div id="add-challenge-modal" class="modal">
        <div class="modal-content" style="max-width: 450px;">
            <div class="modal-header">
                <h3>添加挑战任务</h3> <span class="modal-close-btn" onclick="closeAddChallengeModal()">&times;</span>
            </div>
            <div class="modal-body"> <label for="challenge-todo-select">从任务列表选择 (Page 2 Todos):</label> <select
                    id="challenge-todo-select"></select> <label for="challenge-name-input">任务名称 (显示在Streak面板):</label>
                <input type="text" id="challenge-name-input" placeholder=""> <label for="challenge-desc-input">任务说明
                    (鼠标悬停提示, 可选):</label> <textarea id="challenge-desc-input" placeholder="" rows="2"></textarea>
            </div>
            <div style="text-align: right; padding-top:10px; border-top: 1px solid #eee; margin-top: 15px;"> <button
                    type="button" class="std-btn" onclick="closeAddChallengeModal()"
                    style="margin-right: 5px; background-color: #6c757d;">取消</button> <button id="save-challenge-btn"
                    class="std-btn" style="background-color: #28a745;">开始挑战</button> </div>
        </div>
    </div>
    <div id="streak-task-details-modal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h3 id="streak-details-modal-title">任务详情</h3> <span class="modal-close-btn"
                    onclick="closeStreakDetailsModal()">&times;</span>
            </div>
            <div class="modal-body">
                <h4 style="margin-bottom: 5px;">任务: <span id="details-modal-task-name"></span></h4>
                <p style="font-size: 14px; color: #555;">说明: <span id="details-modal-task-desc"></span></p>
                <div id="details-modal-status-info" style="font-size: 14px;">
                    <p>当前连击: <span id="details-modal-current-streak"></span></p>
                    <p id="details-modal-start-date-container" style="display:none;">开始日期: <span
                            id="details-modal-start-date"></span></p>
                    <p id="details-modal-end-date-container" style="display:none;">结束日期: <span
                            id="details-modal-end-date"></span></p>
                    <p id="details-modal-ended-reason" style="display:none;">结束原因: <span
                            id="details-modal-reason-text"></span></p>
                    <!-- Placeholder for protection points info if needed -->
                </div>
            </div>
            <div style="text-align: right; padding-top:10px; border-top: 1px solid #eee; margin-top: 15px;"> <button
                    id="streak-details-interrupt-btn" class="std-btn"
                    style="background-color: #ffc107; color: #333; display:none;">中断结束</button> <button
                    id="streak-details-restart-btn" class="std-btn"
                    style="background-color: #dc3545; display:none;">清零重新开始</button> <button type="button"
                    class="std-btn" onclick="closeStreakDetailsModal()" style="background-color: #6c757d;">关闭</button>
            </div>
        </div>
    </div>

    <!-- Todo Action Modal (for Clear/Curtain/Storage) -->
    <div id="todo-action-modal" class="modal">
        <div class="modal-content" style="max-width: 380px;">
            <div class="modal-header">
                <h3>操作确认</h3>
                <span class="modal-close-btn" onclick="closeTodoActionModal()">&times;</span>
            </div>
            <div class="modal-body" style="text-align: center;">
                <p>请选择操作：</p>
            </div>
            <div
                style="text-align: center; padding-top:10px; border-top: 1px solid #eee; margin-top: 15px; display: flex; justify-content: center; gap: 8px;">
                <button id="todo-action-modal-clear-btn" class="std-btn"
                    style="background-color: #c5535e; min-width: 80px;">🗑️ 清空</button>
                <button id="todo-action-modal-curtain-btn" class="std-btn"
                    style="background-color: #6c757d; min-width: 80px;">🌒 睡觉</button>
                <button id="todo-action-modal-storage-btn" class="std-btn"
                    style="background-color: #55898d; min-width: 80px;">📦 收纳</button>
            </div>
        </div>
    </div>

    <!-- NEW Todo Schedule Modal -->
    <div id="todo-schedule-modal" class="modal">
        <div class="modal-content" style="max-width: 320px;">
            <div class="modal-header">
                <h3>任务列表操作</h3>
                <span class="modal-close-btn" onclick="closeTodoScheduleModal()">&times;</span>
            </div>
            <div class="modal-body" style="text-align: center;">
                <p>请选择操作：</p>
            </div>
            <div
                style="text-align: center; padding-top:10px; border-top: 1px solid #eee; margin-top: 15px; display: flex; justify-content: center; gap: 8px;">
                <button id="todo-schedule-modal-reset-btn" class="std-btn"
                    style="background-color: #ffc107; color: #333; min-width: 90px;">☀️ 新的一天</button>
                <button id="todo-schedule-modal-arrange-btn" class="std-btn"
                    style="background-color: #4a90e2; min-width: 90px;">🗓️ 安排</button>
            </div>
        </div>
    </div>

    <!-- Todo Storage Modal -->
    <div id="todo-storage-modal" class="modal">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h3>任务收纳箱</h3>
                <span class="modal-close-btn" onclick="closeTodoStorageModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div style="display: flex; gap: 20px; height: 400px;">
                    <!-- 当前任务列表 -->
                    <div style="flex: 1; display: flex; flex-direction: column;">
                        <h4 style="margin-bottom: 10px; text-align: center; color: #444;">当前任务</h4>
                        <div id="storage-current-tasks"
                            style="flex: 1; border: 1px solid #ddd; border-radius: 4px; padding: 10px; overflow-y: auto; background-color: #f9f9f9;">
                            <!-- 当前任务列表将在这里显示 -->
                        </div>
                        <p style="font-size: 12px; color: #666; text-align: center; margin-top: 5px;">点击任务可收纳</p>
                    </div>
                    <!-- 收纳箱 -->
                    <div style="flex: 1; display: flex; flex-direction: column;">
                        <h4 style="margin-bottom: 10px; text-align: center; color: #444;">收纳箱</h4>
                        <div id="storage-stored-tasks"
                            style="flex: 1; border: 1px solid #ddd; border-radius: 4px; padding: 10px; overflow-y: auto; background-color: #f0f8ff;">
                            <!-- 收纳的任务将在这里显示 -->
                        </div>
                        <p style="font-size: 12px; color: #666; text-align: center; margin-top: 5px;">点击任务可取出</p>
                    </div>
                </div>
            </div>
            <div style="text-align: center; padding-top: 10px; border-top: 1px solid #eee; margin-top: 15px;">
                <button class="std-btn" onclick="closeTodoStorageModal()" style="background-color: #6c757d;">关闭</button>
            </div>
        </div>
    </div>

    <!-- NEW: Habit Adjustment Modal -->
    <div id="habit-adjustment-modal" class="modal">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h3>习惯调整</h3>
                <span class="modal-close-btn" onclick="closeHabitAdjustmentModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div style="display: flex; gap: 20px; height: 400px;">
                    <!-- 当前习惯列表 -->
                    <div style="flex: 1; display: flex; flex-direction: column;">
                        <h4 style="margin-bottom: 10px; text-align: center; color: #444;">当前习惯</h4>
                        <div id="adjustment-current-habits"
                            style="flex: 1; border: 1px solid #ddd; border-radius: 4px; padding: 10px; overflow-y: auto; background-color: #f9f9f9;">
                            <!-- 当前习惯将在这里显示 -->
                        </div>
                        <p style="font-size: 12px; color: #666; text-align: center; margin-top: 5px;">点击习惯可收纳</p>
                    </div>
                    <!-- 收纳的习惯 -->
                    <div style="flex: 1; display: flex; flex-direction: column;">
                        <h4 style="margin-bottom: 10px; text-align: center; color: #444;">收纳盒</h4>
                        <div id="adjustment-stored-habits"
                            style="flex: 1; border: 1px solid #ddd; border-radius: 4px; padding: 10px; overflow-y: auto; background-color: #f0f8ff;">
                            <!-- 收纳的习惯将在这里显示 -->
                        </div>
                        <p style="font-size: 12px; color: #666; text-align: center; margin-top: 5px;">点击习惯可取出</p>
                    </div>
                </div>
            </div>
            <div style="text-align: center; padding-top: 10px; border-top: 1px solid #eee; margin-top: 15px;">
                <button class="std-btn" onclick="closeHabitAdjustmentModal()"
                    style="background-color: #6c757d;">关闭</button>
            </div>
        </div>
    </div>

    <!-- NEW: Mood Customization Modal -->
    <div id="mood-customization-modal" class="modal">
        <div class="modal-content" style="max-width: 350px;">
            <div class="modal-header">
                <h3>自定义图标</h3>
                <span class="modal-close-btn" onclick="closeMoodCustomizationModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="mood-input-group">
                    <label for="mood-icon-input1"> 1 :</label>
                    <input type="text" id="mood-icon-input1" maxlength="2">
                </div>
                <div class="mood-input-group">
                    <label for="mood-icon-input2"> 2 :</label>
                    <input type="text" id="mood-icon-input2" maxlength="2">
                </div>
                <div class="mood-input-group">
                    <label for="mood-icon-input3"> 3 :</label>
                    <input type="text" id="mood-icon-input3" maxlength="2">
                </div>
            </div>
            <div style="text-align: right; padding-top: 10px; border-top: 1px solid #eee; margin-top: 15px;">
                <button class="std-btn" onclick="saveMoodCustomization()" style="background-color: #5a6268;">保存</button>
            </div>
        </div>
    </div>


    <!-- Easter Egg Display -->
    <div id="mood-easter-egg"
        style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 150px; z-index: 2000; opacity: 0; transition: opacity 0.3s ease-out, transform 0.3s ease-out; pointer-events: none;">
    </div>

    <!-- Todo Focus Mode Overlay -->
    <div id="todo-focus-overlay" class="todo-focus-overlay">
    </div>

    <script>
        // --- DOM Elements (Keep Existing, add new ones) ---
        const page1 = document.getElementById('page1');
        const page2 = document.getElementById('page2');
        const page3 = document.getElementById('page3');
        const nextPageBtn = document.getElementById('next-page-btn');
        const backToPage1Btn = document.getElementById('back-to-page1-btn');
        const nextPage3Btn = document.getElementById('next-page3-btn');
        const backToPage2Btn = document.getElementById('back-to-page2-btn');
        const p1CompleteBtn = document.getElementById('p1-complete-btn');
        const p1TodoInput = document.getElementById('p1-todo-input');
        const p1Celebration = document.getElementById('p1-celebration');
        const todoList = document.getElementById('todo-list'); const newTodoInput = document.getElementById('new-todo-input'); const addTodoBtn = document.getElementById('add-todo-btn'); const noTasksMsg = document.getElementById('no-tasks-msg'); const todoCongratsLv1 = document.getElementById('todo-congrats-lv1'); const todoCongratsAll = document.getElementById('todo-congrats-all'); const todoCongratAnyLv1 = document.getElementById('todo-congrats-any-lv1'); const p2TodoCelebration = document.getElementById('p2-todo-celebration'); const clearTodosBtn = document.getElementById('clear-todos-btn'); const resetTodosBtn = document.getElementById('reset-todos-btn'); const habitList = document.getElementById('habit-list'); const newHabitTitleInput = document.getElementById('new-habit-title'); const newHabitDescInput = document.getElementById('new-habit-desc'); const addHabitBtn = document.getElementById('add-habit-btn'); const noHabitsMsg = document.getElementById('no-habits-msg'); const habitCelebration = document.getElementById('habit-celebration'); const habitSadness = document.getElementById('habit-sadness'); const statusTriggerBtn = document.getElementById('status-trigger-btn'); const statusContent = document.getElementById('status-content'); const statusQuote = document.getElementById('status-quote'); const statusOptionsContainer = document.getElementById('status-options'); const statusOptionBtns = statusOptionsContainer.querySelectorAll('button'); const statusResetBtn = document.getElementById('status-reset-btn'); const levelManagerModal = document.getElementById('level-manager-modal'); const modalTaskTitle = document.getElementById('modal-task-title'); const modalLevelsList = document.getElementById('modal-levels-list'); const modalAddLevelSection = document.getElementById('modal-add-level-section'); const modalNextLevelNum = document.getElementById('modal-next-level-num'); const modalNewLevelText = document.getElementById('modal-new-level-text'); const modalAddLevelBtn = document.getElementById('modal-add-level-btn');
        const modalDeleteTaskBtn = document.getElementById('modal-delete-task-btn');
        const statusModeToggle = document.getElementById('status-mode-toggle');
        const statusMultiSelectToggle = document.getElementById('status-multi-select-toggle');
        const statusMultiQuoteToggle = document.getElementById('status-multi-quote-toggle'); // New
        const extraStatusSettingGroups = document.querySelectorAll('.extra-status-setting');

        const exportDataBtn = document.getElementById('export-data-btn');
        const importDataBtn = document.getElementById('import-data-btn');
        const importFileInput = document.getElementById('import-file-input');
        const hideIntroBtn = document.getElementById('hide-intro-btn');
        const showIntroBtn = document.getElementById('show-intro-btn');
        const exportDataP3Btn = document.getElementById('export-data-p3-btn');
        const importDataP3Btn = document.getElementById('import-data-p3-btn');
        const simplifyModeBtn = document.getElementById('simplify-mode-btn');
        const page3Tools = document.getElementById('page3-tools');
        const importModal = document.getElementById('import-modal');
        const importFileName = document.getElementById('import-file-name');
        const importFileDate = document.getElementById('import-file-date');
        const importTodosCheck = document.getElementById('import-todos');
        const importHabitsCheck = document.getElementById('import-habits');
        const importUrlSettingsCheck = document.getElementById('import-url-settings');
        const importStatusSettingsCheck = document.getElementById('import-status-settings');
        const importTablesCheck = document.getElementById('import-tables');
        const importQACheck = document.getElementById('import-qa');
        const importTodayStreakCheck = document.getElementById('import-today-streak');
        const importDailyRecordsCheck = document.getElementById('import-daily-records');

        const tableTabsContainer = document.getElementById('table-tabs');
        const tableNameInput = document.getElementById('table-name-input');
        const tableRenderArea = document.getElementById('table-render-area');
        const tableAddColBtn = document.getElementById('table-add-col-btn');
        const tableAddRowBtn = document.getElementById('table-add-row-btn');
        const tableDelColBtn = document.getElementById('table-del-col-btn');
        const tableDelRowBtn = document.getElementById('table-del-row-btn');
        const tableClearBtn = document.getElementById('table-clear-btn');
        const qaModule = document.getElementById('qa-module');
        const qaContentArea = document.getElementById('qa-content-area');
        const qaAddBtn = document.getElementById('qa-add-btn');
        const qaEditBtn = document.getElementById('qa-edit-btn');
        const qaClearAnswersBtn = document.getElementById('qa-clear-answers-btn');
        const qaGroupsBtn = document.getElementById('qa-groups-btn');
        const qaToggleFoldBtn = document.getElementById('qa-toggle-fold-btn');
        const qaFoldedPlaceholder = document.getElementById('qa-folded-placeholder');
        const qaEditorModal = document.getElementById('qa-editor-modal');
        const qaEditorList = document.getElementById('qa-editor-list');
        const qaAnswerModal = document.getElementById('qa-answer-modal');
        const qaAnswerModalQuestion = document.getElementById('qa-answer-modal-question');
        const qaAnswerModalInput = document.getElementById('qa-answer-modal-input');
        const qaAnswerModalSaveBtn = document.getElementById('qa-answer-modal-save-btn');
        const qaGroupsModal = document.getElementById('qa-groups-modal');
        const qaGroupsList = document.getElementById('qa-groups-list');
        const qaGroupNameInput = document.getElementById('qa-group-name-input');
        const qaSaveGroupBtn = document.getElementById('qa-save-group-btn');
        const qaCurrentPreview = document.getElementById('qa-current-preview');


        const todayLeftPanel = document.getElementById('today-left-panel');
        const todayTitle = document.getElementById('today-title'); // NEW
        const todayCurrentRatingDisplay = document.getElementById('today-current-rating');
        const todayRatingButtonsContainer = document.querySelector('#today-left-panel .today-rating-buttons');
        const moodAngryCountDisplay = document.getElementById('mood-angry-count');
        const moodNeutralCountDisplay = document.getElementById('mood-neutral-count');
        const moodHurtCountDisplay = document.getElementById('mood-hurt-count');
        const moodAngryIcon = document.getElementById('mood-angry-icon'); // NEW
        const moodNeutralIcon = document.getElementById('mood-neutral-icon'); // NEW
        const moodHurtIcon = document.getElementById('mood-hurt-icon'); // NEW
        const moodIcons = document.querySelectorAll('#today-left-panel .mood-icon');
        const todayRightStreakPanel = document.getElementById('today-right-streak-panel');
        const streakCountDisplay = document.getElementById('streak-count-display');
        const streakTaskNameDisplay = document.getElementById('streak-task-name-display');
        const streakActionButtonsContainer = document.getElementById('streak-action-buttons-container');
        const streakAddChallengeBtn = document.getElementById('streak-add-challenge-btn');
        const streakDoneBtn = document.getElementById('streak-done-btn');
        const streakProtectBtn = document.getElementById('streak-protect-btn');
        const streakEndedBtn = document.getElementById('streak-ended-btn');
        const streakProtectionPointsDisplay = document.getElementById('streak-protection-points-display');
        const streakProtectionWarning = document.getElementById('streak-protection-warning');
        const addChallengeModal = document.getElementById('add-challenge-modal');
        const challengeTodoSelect = document.getElementById('challenge-todo-select');
        const challengeNameInput = document.getElementById('challenge-name-input');
        const challengeDescInput = document.getElementById('challenge-desc-input');
        const saveChallengeBtn = document.getElementById('save-challenge-btn');
        const streakTaskDetailsModal = document.getElementById('streak-task-details-modal');
        const streakDetailsModalTitle = document.getElementById('streak-details-modal-title');
        const detailsModalTaskName = document.getElementById('details-modal-task-name');
        const detailsModalTaskDesc = document.getElementById('details-modal-task-desc');
        const detailsModalCurrentStreak = document.getElementById('details-modal-current-streak');
        const detailsModalStartDateContainer = document.getElementById('details-modal-start-date-container');
        const detailsModalStartDate = document.getElementById('details-modal-start-date');
        const detailsModalEndDateContainer = document.getElementById('details-modal-end-date-container');
        const detailsModalEndDate = document.getElementById('details-modal-end-date');
        const detailsModalEndedReason = document.getElementById('details-modal-ended-reason');
        const detailsModalReasonText = document.getElementById('details-modal-reason-text');
        const streakDetailsInterruptBtn = document.getElementById('streak-details-interrupt-btn');
        const streakDetailsRestartBtn = document.getElementById('streak-details-restart-btn');

        const recordsModuleContainer = document.getElementById('records-module-container');
        const recordViewWeekBtn = document.getElementById('record-view-week');
        const recordViewMonthBtn = document.getElementById('record-view-month');
        const clearRecordsBtn = document.getElementById('clear-records-btn');
        const recordsChartArea = document.getElementById('records-chart-area');
        const recordsPlaceholderMsg = recordsChartArea.querySelector('.placeholder-msg');
        const moodEasterEggDiv = document.getElementById('mood-easter-egg');
        let todoFocusOverlay; // 延迟初始化

        // Todo Action Modal (Clear/Curtain/Storage)
        const todoActionModal = document.getElementById('todo-action-modal');
        const todoActionModalClearBtn = document.getElementById('todo-action-modal-clear-btn');
        const todoActionModalCurtainBtn = document.getElementById('todo-action-modal-curtain-btn');
        const todoActionModalStorageBtn = document.getElementById('todo-action-modal-storage-btn');

        // Todo Schedule Modal (Reset/Arrange)
        const todoScheduleModal = document.getElementById('todo-schedule-modal');
        const todoScheduleModalResetBtn = document.getElementById('todo-schedule-modal-reset-btn');
        const todoScheduleModalArrangeBtn = document.getElementById('todo-schedule-modal-arrange-btn');

        // Todo Storage Modal
        const todoStorageModal = document.getElementById('todo-storage-modal');
        const storageCurrentTasks = document.getElementById('storage-current-tasks');
        const storageStoredTasks = document.getElementById('storage-stored-tasks');

        // NEW: Habit Adjustment
        const habitAdjustmentBtn = document.getElementById('habit-adjustment-btn');
        const habitAdjustmentModal = document.getElementById('habit-adjustment-modal');
        const adjustmentCurrentHabits = document.getElementById('adjustment-current-habits');
        const adjustmentStoredHabits = document.getElementById('adjustment-stored-habits');

        // NEW: Mood Customization
        const moodCustomizationModal = document.getElementById('mood-customization-modal');
        const moodIconInput1 = document.getElementById('mood-icon-input1');
        const moodIconInput2 = document.getElementById('mood-icon-input2');
        const moodIconInput3 = document.getElementById('mood-icon-input3');

        // NEW: Month Navigation
        const recordsPrevMonthBtn = document.getElementById('records-prev-month-btn');
        const recordsNextMonthBtn = document.getElementById('records-next-month-btn');


        // --- Data Storage (Keep Existing, add new ones) ---
        let todos = [];
        let habits = [];
        let storedHabits = []; // NEW
        let urlSettings = { 1: { url: '', emoji: '🔗' }, 2: { url: '', emoji: '⛳' } };
        let statusData = [ /* Populated by loadStatusSettings */];
        let currentStatusMode = '4-state'; // '4-state' or '8-state'
        let allowMultiStatus = false;
        let allowMultiQuotesPerStatus = false; // New
        let statusQuoteSelections = {}; // 存储每个状态当前选中的鼓励语索引
        let tablesData = {};
        let activeTableId = null;
        let qaData = [];
        let qaGroups = []; // 存储问题组数据
        let isQAFolded = false;
        let currentEditingTaskIndex = -1;
        let importData = null;
        let currentEditingQAItemId = null;
        let isTodoCurtained = false;
        let isIntroHidden = false;
        let isSimplifiedMode = false; // Track simplified input mode
        let isArrangeMode = false;
        let focusedTodoId = null;
        let storedTodos = [];
        let isFirstTaskAdded = false;
        let timeBlockRainbowStates = {}; // 存储每个时间段的彩蛋状态

        // MODIFIED: Added moodIcons
        let todaySectionData = { rating: 0, moods: { angry: 0, neutral: 0, hurt: 0 }, lastActivityDate: new Date().toDateString(), moodClickCounts: { angry: 0, neutral: 0, hurt: 0 }, moodIcons: { angry: '😡', neutral: '😑', hurt: '🤕' } };
        let streakData = { taskId: null, taskName: '', taskDesc: '', currentStreak: 0, longestStreak: 0, startDate: null, endDate: null, lastCompletedDate: null, status: 'NOT_STARTED', protectionPoints: 2, maxProtectionPoints: 2, protectionReplenishCounter: 0, endedReason: '' };

        let dailyRecords = [];
        let currentRecordView = 'week';
        let chartDisplayDate = new Date(); // NEW for month navigation


        // --- Local Storage Keys (Keep Existing, add new ones) ---
        const LS_PAGE_KEY = 'actionStarter_lastPage';
        const LS_TODOS_KEY = 'actionStarter_todos';
        const LS_HABITS_KEY = 'actionStarter_habits';
        const LS_STORED_HABITS_KEY = 'actionStarter_storedHabits'; // NEW
        const LS_URL1_KEY = 'actionStarter_urlSetting1';
        const LS_URL2_KEY = 'actionStarter_urlSetting2';
        const LS_STATUS_KEY = 'actionStarter_statusData';
        const LS_STATUS_MODE_KEY = 'actionStarter_statusMode';
        const LS_STATUS_MULTI_SELECT_KEY = 'actionStarter_statusMultiSelect';
        const LS_STATUS_MULTI_QUOTE_MODE_KEY = 'actionStarter_statusMultiQuoteMode'; // New
        const LS_TABLES_KEY = 'actionStarter_tablesData';
        const LS_ACTIVE_TABLE_KEY = 'actionStarter_activeTableId';
        const LS_QA_KEY = 'actionStarter_qaData';
        const LS_QA_GROUPS_KEY = 'actionStarter_qaGroups';
        const LS_QA_FOLDED_KEY = 'actionStarter_qaFolded';
        const LS_TODAY_DATA_KEY = 'actionStarter_todayData';
        const LS_STREAK_DATA_KEY = 'actionStarter_streakData';
        const LS_DAILY_RECORDS_KEY = 'actionStarter_dailyRecords';
        const LS_TODO_CURTAIN_KEY = 'actionStarter_isTodoCurtained';
        const LS_HIDE_INTRO_KEY = 'actionStarter_hideIntro';
        const LS_SIMPLIFIED_MODE_KEY = 'actionStarter_simplifiedMode';
        const LS_TODO_ARRANGE_MODE_KEY = 'actionStarter_isArrangeMode';
        const LS_STORED_TODOS_KEY = 'actionStarter_storedTodos';
        const LS_FIRST_TASK_ADDED_KEY = 'actionStarter_firstTaskAdded';
        const LS_TIME_BLOCK_RAINBOW_KEY = 'actionStarter_timeBlockRainbowMode';


        // --- Utility Functions (Keep Existing) ---
        function generateId() { return 'id' + Date.now().toString(36) + Math.random().toString(36).substr(2, 5); }
        function escapeHTML(str) { if (typeof str !== 'string') return ''; return str.replace(/[&<>'"]/g, tag => ({ '&': '&amp;', '<': '&lt;', '>': '&gt;', "'": '&#39;', '"': '&quot;' }[tag] || tag)); }
        function triggerAnimation(element) { element.classList.add('animate'); setTimeout(() => element.classList.remove('animate'), 1500); }
        function toggleSettings(panelId) { const panel = document.getElementById(panelId); if (!panel) return; const willBeVisible = panel.style.display !== 'block'; document.querySelectorAll('.settings-panel').forEach(p => p.style.display = 'none'); if (willBeVisible) panel.style.display = 'block'; }

        // 性能监控函数
        let performanceStats = {
            domQueries: 0,
            timerCalls: 0,
            lastReset: Date.now()
        };

        function logPerformance(type) {
            performanceStats[type]++;
        }

        function getPerformanceReport() {
            const elapsed = (Date.now() - performanceStats.lastReset) / 1000;
            return `性能报告 (${elapsed.toFixed(1)}秒内):\n` +
                   `DOM查询: ${performanceStats.domQueries}\n` +
                   `定时器调用: ${performanceStats.timerCalls}\n` +
                   `平均DOM查询/秒: ${(performanceStats.domQueries / elapsed).toFixed(2)}`;
        }

        function resetPerformanceStats() {
            performanceStats = {
                domQueries: 0,
                timerCalls: 0,
                lastReset: Date.now()
            };
        }

        // 在控制台添加性能监控命令
        window.getPerformanceReport = getPerformanceReport;
        window.resetPerformanceStats = resetPerformanceStats;

        function getLocalDateString(date = new Date()) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        }


        // --- Page Navigation & Persistence (Keep Existing, update Page 3 loading) ---
        function updateIntroVisibility() {
            if (isIntroHidden) {
                backToPage1Btn.style.display = 'none';
                page3Tools.style.display = 'flex';
            } else {
                backToPage1Btn.style.display = 'block';
                page3Tools.style.display = 'none';
            }
        }

        function navigateTo(pageId) {
            page1.classList.remove('active');
            page2.classList.remove('active');
            page3.classList.remove('active');
            let activePageElement = null;

            if (isIntroHidden && pageId === 'page1') {
                pageId = 'page2';
            }

            switch (pageId) {
                case 'page1': activePageElement = page1; break;
                case 'page2':
                    activePageElement = page2;
                    renderTodoList(); renderHabitList();
                    updateWidgetColumnLayout();
                    break;
                case 'page3':
                    activePageElement = page3;
                    if (Object.keys(tablesData).length === 0 && localStorage.getItem(LS_TABLES_KEY)) loadTablesData();
                    if (qaData.length === 0 && localStorage.getItem(LS_QA_KEY)) loadQAData();
                    renderTableModule(); renderQAModule();
                    renderTodayLeftPanel();
                    renderStreakModule();
                    renderRecordsModule();
                    break;
                default:
                    if (isIntroHidden) {
                        activePageElement = page2;
                        pageId = 'page2';
                        renderTodoList(); renderHabitList();
                        updateWidgetColumnLayout();
                    } else {
                        activePageElement = page1;
                        pageId = 'page1';
                    }
                    break;
            }
            if (activePageElement) { activePageElement.classList.add('active'); localStorage.setItem(LS_PAGE_KEY, pageId); }
        }
        nextPageBtn.addEventListener('click', () => navigateTo('page2'));
        backToPage1Btn.addEventListener('click', () => navigateTo('page1'));
        nextPage3Btn.addEventListener('click', () => navigateTo('page3'));
        backToPage2Btn.addEventListener('click', () => navigateTo('page2'));

        // --- Page 1: Simple Todo (Keep Existing) ---
        p1CompleteBtn.addEventListener('click', () => { const taskText = p1TodoInput.value.trim(); if (taskText !== '' && !p1TodoInput.disabled) { triggerAnimation(p1Celebration); p1TodoInput.disabled = true; p1TodoInput.classList.add('completed'); p1CompleteBtn.disabled = true; } else if (taskText === '') { alert('请输入你要完成的任务！'); } });

        // --- Records Module Data Functions ---
        function saveDailyRecords() {
            localStorage.setItem(LS_DAILY_RECORDS_KEY, JSON.stringify(dailyRecords));
        }
        function loadDailyRecords() {
            const saved = localStorage.getItem(LS_DAILY_RECORDS_KEY);
            if (saved) {
                try {
                    const parsed = JSON.parse(saved);
                    if (Array.isArray(parsed)) {
                        dailyRecords = parsed.filter(r => r && r.date);
                        dailyRecords.forEach(r => {
                            if (!r.moods) r.moods = { angry: 0, neutral: 0, hurt: 0 };
                        });
                    } else {
                        throw new Error("Invalid daily records format");
                    }
                } catch (e) {
                    console.error("Error loading Daily Records:", e);
                    localStorage.removeItem(LS_DAILY_RECORDS_KEY);
                    dailyRecords = [];
                }
            }
        }
        function getRecordForDate(dateString) {
            return dailyRecords.find(r => r.date === dateString);
        }
        function getOrCreateRecordForDate(dateString) {
            let record = dailyRecords.find(r => r.date === dateString);
            if (!record) {
                record = {
                    date: dateString,
                    tasksCompleted: 0,
                    habitsIncremented: 0,
                    todayRating: 0,
                    moods: { angry: 0, neutral: 0, hurt: 0 }
                };
                dailyRecords.push(record);
                dailyRecords.sort((a, b) => new Date(a.date) - new Date(b.date));
            } else if (!record.moods) {
                record.moods = { angry: 0, neutral: 0, hurt: 0 };
            }
            return record;
        }


        // --- Page 2: Todo List ---
        function saveTodos() { localStorage.setItem(LS_TODOS_KEY, JSON.stringify(todos)); }
        function saveIsTodoCurtained() { localStorage.setItem(LS_TODO_CURTAIN_KEY, JSON.stringify(isTodoCurtained)); }
        function loadIsTodoCurtained() {
            const saved = localStorage.getItem(LS_TODO_CURTAIN_KEY);
            isTodoCurtained = saved ? JSON.parse(saved) : false;
        }

        function saveIsIntroHidden() {
            localStorage.setItem(LS_HIDE_INTRO_KEY, JSON.stringify(isIntroHidden));
        }
        function loadIsIntroHidden() {
            const saved = localStorage.getItem(LS_HIDE_INTRO_KEY);
            isIntroHidden = saved ? JSON.parse(saved) : false;
            updateIntroVisibility();
        }

        function updateHideIntroButtonVisibility() {
            if (isFirstTaskAdded) {
                hideIntroBtn.style.display = 'block';
            } else {
                hideIntroBtn.style.display = 'none';
            }
        }

        function saveSimplifiedMode() {
            localStorage.setItem(LS_SIMPLIFIED_MODE_KEY, JSON.stringify(isSimplifiedMode));
        }

        function loadSimplifiedMode() {
            const saved = localStorage.getItem(LS_SIMPLIFIED_MODE_KEY);
            isSimplifiedMode = saved ? JSON.parse(saved) : false;
            updateSimplifiedModeUI();
        }

        function updateSimplifiedModeUI() {
            const page2Container = document.querySelector('.page2-container');
            if (!page2Container) return;

            if (isSimplifiedMode) {
                page2Container.classList.add('simplified-mode');
                simplifyModeBtn.textContent = '📖 原始框';
                simplifyModeBtn.title = '恢复原始添加框';
            } else {
                page2Container.classList.remove('simplified-mode');
                simplifyModeBtn.textContent = '📝 再简化';
                simplifyModeBtn.title = '简化添加框';
            }
        }

        function toggleSimplifiedMode() {
            isSimplifiedMode = !isSimplifiedMode;
            saveSimplifiedMode();
            updateSimplifiedModeUI();

            if (isSimplifiedMode) {
                alert('现在点击栏标题后才显示添加框');
            }
        }

        function toggleInputArea(columnId) {
            if (!isSimplifiedMode) return;
            const inputArea = document.querySelector(`#${columnId} .input-area`);
            if (!inputArea) return;
            const isVisible = inputArea.style.display === 'block' || inputArea.style.display === 'flex';
            inputArea.style.display = isVisible ? 'none' : 'flex';
        }

        function saveIsArrangeMode() { localStorage.setItem(LS_TODO_ARRANGE_MODE_KEY, JSON.stringify(isArrangeMode)); }
        function loadIsArrangeMode() {
            const saved = localStorage.getItem(LS_TODO_ARRANGE_MODE_KEY);
            isArrangeMode = saved ? JSON.parse(saved) : false;
        }

        function saveTimeBlockRainbowStates() {
            localStorage.setItem(LS_TIME_BLOCK_RAINBOW_KEY, JSON.stringify(timeBlockRainbowStates));
        }
        function loadTimeBlockRainbowStates() {
            const saved = localStorage.getItem(LS_TIME_BLOCK_RAINBOW_KEY);
            timeBlockRainbowStates = saved ? JSON.parse(saved) : {};
        }

        const timeBlockDividerTexts = ["————————上午————————", "————————下午————————", "————————晚上————————", "————————凌晨————————"];
        function getCurrentTimeBlockIndex() {
            const currentHour = new Date().getHours();
            if (currentHour >= 6 && currentHour <= 11) return 0; // Morning
            if (currentHour >= 12 && currentHour <= 17) return 1; // Afternoon
            if (currentHour >= 18 && currentHour <= 23) return 2; // Evening
            if (currentHour >= 0 && currentHour <= 5) return 3; // Midnight
            return 0;
        }


        function createTodoLi(task) {
            const li = document.createElement('li');
            li.className = 'todo-item';
            li.id = `task-${task.id}`;
            const isCurrentLevelDone = task.levels[task.currentLevelIndex].done;
            const currentLevel = task.levels[task.currentLevelIndex];
            const buttonClass = isCurrentLevelDone ? 'undo-btn' : `complete-btn level-${currentLevel.level}`;
            const maxLevel = task.levels.length;

            const isFirstTask = (!isFirstTaskAdded && todos.length === 1);
            const rightClickHint = isFirstTask ? '<div style="font-size: 12px; color: #888; margin-top: 5px; text-align: center;">右键可以聚焦显示该任务</div>' : '';
            const levelHint = isFirstTask ? '<span style="font-size: 11px; color: #999; margin-left: 5px;">点击管理任务层级</span>' : '';

            li.innerHTML = ` <span class="level-indicator max-level-${maxLevel}" onclick="openLevelManager('${task.id}')" title="管理层级">lv${currentLevel.level}</span>${levelHint} <span class="todo-item-text ${isCurrentLevelDone ? 'done' : ''}">${escapeHTML(currentLevel.text)}</span> <div class="todo-item-controls"> <button class="${buttonClass}" onclick="toggleLevelComplete('${task.id}', ${task.currentLevelIndex})"> ${isCurrentLevelDone ? '撤销' : '完成'} </button> </div> ${rightClickHint}`;

            li.addEventListener('contextmenu', function (event) {
                if (isTodoCurtained) return;
                event.preventDefault();
                enterFocusMode(task.id);
                event.stopPropagation();
            });
            return li;
        }

        function createDividerLi(text, timeBlockIndex) {
            const dividerLi = document.createElement('li');
            dividerLi.className = 'todo-item time-block-divider';
            dividerLi.textContent = text;
            dividerLi.draggable = false;
            dividerLi.dataset.timeBlockIndex = timeBlockIndex;

            if (timeBlockRainbowStates[timeBlockIndex] === true) {
                dividerLi.classList.add('rainbow-mode');
            }

            if (isArrangeMode) {
                const currentTimeBlockIdx = getCurrentTimeBlockIndex();
                if (timeBlockIndex === currentTimeBlockIdx) {
                    dividerLi.classList.add('current-time-block');
                }
                dividerLi.addEventListener('contextmenu', function (event) {
                    event.preventDefault();
                    this.classList.toggle('rainbow-mode');
                    timeBlockRainbowStates[timeBlockIndex] = this.classList.contains('rainbow-mode');
                    saveTimeBlockRainbowStates();
                    updateTimeBlockTasksBlur();
                    event.stopPropagation();
                });
            }
            return dividerLi;
        }

        function renderTodoList() {
            clearTodosBtn.textContent = '🗑️ 清空/拉窗帘';
            clearTodosBtn.disabled = false;

            if (isTodoCurtained) {
                todoList.innerHTML = '<p class="placeholder-msg" style="font-size: 24px; margin-top: 50px; text-align:center;">🌒zzZZZ~~</p>';
                noTasksMsg.style.display = 'none';
                todoCongratsLv1.style.display = 'none';
                todoCongratsAll.style.display = 'none';
                if (todoCongratAnyLv1) todoCongratAnyLv1.style.display = 'none';
                newTodoInput.disabled = true;
                addTodoBtn.disabled = true;
                resetTodosBtn.disabled = true;
                return;
            }

            newTodoInput.disabled = false;
            addTodoBtn.disabled = false;
            resetTodosBtn.disabled = isTodoCurtained || (todos.length === 0 && !isArrangeMode);

            todoList.innerHTML = '';
            const hasTasks = todos.length > 0;

            if (isArrangeMode) {
                timeBlockDividerTexts.forEach((text, timeBlockIndex) => {
                    const dividerLi = createDividerLi(text, timeBlockIndex);
                    todoList.appendChild(dividerLi);

                    if (hasTasks) {
                        todos.forEach((task) => {
                            if (!task || !task.levels || task.levels.length === 0) { console.error("Invalid task structure:", task); return; }
                            if (task.currentLevelIndex === undefined || task.currentLevelIndex < 0 || task.currentLevelIndex >= task.levels.length) { task.currentLevelIndex = 0; console.warn("Corrected invalid currentLevelIndex for task:", task.id); }
                            if (task.timeBlock === undefined) { task.timeBlock = 0; }
                            if (task.timeBlock === timeBlockIndex) {
                                const li = createTodoLi(task);
                                li.draggable = true;
                                li.addEventListener('dragstart', handleDragStart);
                                li.style.cursor = 'grab';
                                todoList.appendChild(li);
                            }
                        });
                    }
                });
            } else {
                if (hasTasks) {
                    todos.forEach((task) => {
                        if (!task || !task.levels || task.levels.length === 0) { console.error("Invalid task structure:", task); return; }
                        if (task.currentLevelIndex === undefined || task.currentLevelIndex < 0 || task.currentLevelIndex >= task.levels.length) { task.currentLevelIndex = 0; console.warn("Corrected invalid currentLevelIndex for task:", task.id); }
                        const li = createTodoLi(task);
                        li.draggable = false;
                        li.style.cursor = 'pointer';
                        todoList.appendChild(li);
                    });
                }
            }

            noTasksMsg.style.display = (hasTasks || isArrangeMode) ? 'none' : 'block';
            updateCongratsMessages();

            if (isArrangeMode) {
                todoList.removeEventListener('dragover', handleDragOver);
                todoList.addEventListener('dragover', handleDragOver);
                todoList.removeEventListener('drop', handleDrop);
                todoList.addEventListener('drop', handleDrop);
            } else {
                todoList.removeEventListener('dragover', handleDragOver);
                todoList.removeEventListener('drop', handleDrop);
            }
            // 清除缓存，因为DOM结构改变了
            clearTimeBlockCache();
            clearTaskItemsCache();

            updateAnimatedTimeBlock();

            // 根据模式启动或停止定时器
            if (isArrangeMode) {
                startAnimationTimer();
            } else {
                stopAnimationTimer();
            }
        }

        function handleDragStart(e) {
            if (!isArrangeMode) return;
            e.dataTransfer.setData('text/plain', e.target.id);
            e.dataTransfer.effectAllowed = 'move';
            e.target.style.opacity = '0.5';
        }

        function handleDragOver(e) {
            if (!isArrangeMode) return;
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';
        }

        function handleDrop(e) {
            if (!isArrangeMode) return;
            e.preventDefault();
            const draggedItemId = e.dataTransfer.getData('text/plain');
            const draggedElement = document.getElementById(draggedItemId);

            if (draggedElement) {
                draggedElement.style.opacity = '1';
                let targetElement = e.target.closest('li.todo-item');

                if (targetElement && draggedElement !== targetElement) {
                    const rect = targetElement.getBoundingClientRect();
                    const isAfter = e.clientY > rect.top + rect.height / 2;
                    targetElement.parentNode.insertBefore(draggedElement, isAfter ? targetElement.nextSibling : targetElement);
                } else if (!targetElement && todoList.contains(e.target)) {
                    let droppedInDivider = e.target.closest('.time-block-divider');
                    if (droppedInDivider) {
                        droppedInDivider.parentNode.insertBefore(draggedElement, droppedInDivider.nextSibling);
                    } else {
                        todoList.appendChild(draggedElement);
                    }
                }
                updateTodosOrderFromDOM();
                saveTodos();
            }
        }

        function updateTodosOrderFromDOM() {
            const newTodosOrder = [];
            let currentTimeBlock = 0;
            Array.from(todoList.children).forEach(li => {
                if (li.classList.contains('time-block-divider')) {
                    currentTimeBlock = parseInt(li.dataset.timeBlockIndex, 10);
                } else if (li.classList.contains('todo-item')) {
                    const taskId = li.id.replace('task-', '');
                    const task = todos.find(t => t.id === taskId);
                    if (task) {
                        task.timeBlock = currentTimeBlock;
                        newTodosOrder.push(task);
                    }
                }
            });
            todos = newTodosOrder;
        }
        function arrangeTasksToMorning() {
            todos.forEach(task => { if (task.timeBlock === undefined) { task.timeBlock = 0; } });
            saveTodos();
        }

        function enterFocusMode(taskId) {
            if (isTodoCurtained) return;
            exitFocusMode();
            if (!todoFocusOverlay) todoFocusOverlay = document.getElementById('todo-focus-overlay');
            if (!todoFocusOverlay) return;

            focusedTodoId = taskId;
            const focusedElement = document.getElementById(`task-${taskId}`);
            if (focusedElement) {
                focusedElement.classList.add('focused');

                const task = todos.find(t => t.id === taskId);
                if (task) {
                    const currentLevel = task.levels[task.currentLevelIndex];
                    if (currentLevel && currentLevel.auxInfo) {
                        const auxInfoDiv = document.createElement('div');
                        auxInfoDiv.id = 'focus-aux-info';
                        auxInfoDiv.textContent = currentLevel.auxInfo;
                        document.body.appendChild(auxInfoDiv);

                        const rect = focusedElement.getBoundingClientRect();
                        const auxInfoWidth = auxInfoDiv.offsetWidth;

                        auxInfoDiv.style.top = `${rect.bottom + 5}px`;
                        auxInfoDiv.style.left = `${rect.right - auxInfoWidth}px`;
                    }
                }
            }
            todoFocusOverlay.classList.add('active');
        }

        function exitFocusMode() {
            document.getElementById('focus-aux-info')?.remove();
            if (!todoFocusOverlay) todoFocusOverlay = document.getElementById('todo-focus-overlay');
            if (!todoFocusOverlay) return;
            focusedTodoId = null;
            document.querySelectorAll('.todo-item.focused').forEach(el => el.classList.remove('focused'));
            todoFocusOverlay.classList.remove('active');
        }

        function updateCongratsMessages() {
            if (todoCongratAnyLv1) todoCongratAnyLv1.style.display = 'none';
            todoCongratsLv1.style.display = 'none';
            todoCongratsAll.style.display = 'none';

            if (todos.length === 0 || isTodoCurtained) return;

            let anyLv1Done = false;
            let allLv1Done = true;
            let allHighestDone = true;

            for (const task of todos) {
                if (task.levels[0]?.done) { anyLv1Done = true; } else { allLv1Done = false; }
                const highestLevel = task.levels[task.levels.length - 1];
                if (!highestLevel?.done) { allHighestDone = false; }
            }

            if (allHighestDone) { todoCongratsAll.style.display = 'block'; }
            else if (allLv1Done) { todoCongratsLv1.style.display = 'block'; }
            else if (anyLv1Done && todoCongratAnyLv1) { todoCongratAnyLv1.style.display = 'block'; }
        }

        function addTodo() {
            const text = newTodoInput.value.trim();
            if (text) {
                const newTaskId = generateId();
                const newTask = {
                    id: newTaskId,
                    currentLevelIndex: 0,
                    levels: [{ level: 1, text: text, done: false, auxInfo: '' }]
                };
                if (isArrangeMode) { newTask.timeBlock = getCurrentTimeBlockIndex(); }
                const wasFirstTask = todos.length === 0 && !isFirstTaskAdded;
                todos.push(newTask);
                newTodoInput.value = '';
                saveTodos();
                renderTodoList();
                if (wasFirstTask) {
                    isFirstTaskAdded = true;
                    saveFirstTaskAdded();
                    updateHideIntroButtonVisibility();
                    setTimeout(() => { enterFocusMode(newTaskId); }, 100);
                }
                if (isSimplifiedMode) {
                    const todoInputArea = document.querySelector('#todo-column .input-area');
                    if (todoInputArea) todoInputArea.style.display = 'none';
                }
            }
        }
        function findTaskIndexById(taskId) { return todos.findIndex(task => task.id === taskId); }

        function toggleLevelComplete(taskId, levelIndex) {
            const taskIndex = findTaskIndexById(taskId); // BUG FIX: This line was missing
            if (taskIndex === -1 || !todos[taskIndex].levels[levelIndex]) return;

            const task = todos[taskIndex];
            const level = task.levels[levelIndex];
            const wasDone = level.done;
            level.done = !level.done;

            if (!wasDone && level.done) {
                const todayStr = getLocalDateString();
                const todayRecord = getOrCreateRecordForDate(todayStr);
                todayRecord.tasksCompleted = (todayRecord.tasksCompleted || 0) + 1;
                saveDailyRecords();
                if (page3.classList.contains('active')) renderRecordsModule();

                if (levelIndex + 1 < task.levels.length) {
                    task.currentLevelIndex++;
                }
                triggerAnimation(p2TodoCelebration);
            } else if (wasDone && !level.done) {
                const todayStr = getLocalDateString();
                const todayRecord = getRecordForDate(todayStr);
                if (todayRecord && todayRecord.tasksCompleted > 0) {
                    todayRecord.tasksCompleted--;
                    saveDailyRecords();
                    if (page3.classList.contains('active')) renderRecordsModule();
                }
            }
            if (focusedTodoId === taskId) {
                exitFocusMode();
            }
            saveTodos();
            renderTodoList();
            updateCongratsMessages();
        }

        function openTodoActionModal() {
            todoActionModalClearBtn.disabled = todos.length === 0 && !isTodoCurtained;
            todoActionModalCurtainBtn.textContent = isTodoCurtained ? '🌝 没睡觉' : '🌒 睡觉';
            todoActionModal.style.display = 'flex';
        }
        function closeTodoActionModal() { todoActionModal.style.display = 'none'; }

        function performClearAllTodos() {
            if (todos.length > 0) { todos = []; saveTodos(); }
            if (isTodoCurtained) { isTodoCurtained = false; saveIsTodoCurtained(); }
            renderTodoList();
        }
        function saveStoredTodos() { localStorage.setItem(LS_STORED_TODOS_KEY, JSON.stringify(storedTodos)); }
        function loadStoredTodos() {
            const saved = localStorage.getItem(LS_STORED_TODOS_KEY);
            if (saved) { try { storedTodos = JSON.parse(saved); } catch (e) { console.error("Error loading stored todos:", e); localStorage.removeItem(LS_STORED_TODOS_KEY); storedTodos = []; } }
        }
        function saveFirstTaskAdded() { localStorage.setItem(LS_FIRST_TASK_ADDED_KEY, JSON.stringify(isFirstTaskAdded)); }
        function loadFirstTaskAdded() {
            const saved = localStorage.getItem(LS_FIRST_TASK_ADDED_KEY);
            isFirstTaskAdded = saved ? JSON.parse(saved) : false;
        }
        function openTodoStorageModal() { renderStorageModal(); todoStorageModal.style.display = 'flex'; }
        function closeTodoStorageModal() { todoStorageModal.style.display = 'none'; }

        function renderStorageModal() {
            storageCurrentTasks.innerHTML = '';
            if (todos.length === 0) { storageCurrentTasks.innerHTML = '<p style="text-align: center; color: #888; margin-top: 20px;">暂无任务</p>'; }
            else { todos.forEach(task => { const taskDiv = document.createElement('div'); taskDiv.className = 'storage-item'; taskDiv.innerHTML = `<span><strong>Lv${task.levels[task.currentLevelIndex].level}</strong> ${escapeHTML(task.levels[task.currentLevelIndex].text)}</span>`; taskDiv.addEventListener('click', () => storeTask(task.id)); storageCurrentTasks.appendChild(taskDiv); }); }
            storageStoredTasks.innerHTML = '';
            if (storedTodos.length === 0) { storageStoredTasks.innerHTML = '<p style="text-align: center; color: #888; margin-top: 20px;">收纳箱为空</p>'; }
            else { storedTodos.forEach(task => { const taskDiv = document.createElement('div'); taskDiv.className = 'storage-item'; taskDiv.innerHTML = `<span><strong>Lv${task.levels[task.currentLevelIndex].level}</strong> ${escapeHTML(task.levels[task.currentLevelIndex].text)}</span>`; taskDiv.addEventListener('click', () => restoreTask(task.id)); storageStoredTasks.appendChild(taskDiv); }); }
        }
        function storeTask(taskId) {
            const taskIndex = findTaskIndexById(taskId); if (taskIndex === -1) return; const task = todos[taskIndex]; storedTodos.push(task); todos.splice(taskIndex, 1); saveTodos(); saveStoredTodos(); renderTodoList(); renderStorageModal();
        }
        function restoreTask(taskId) {
            const taskIndex = storedTodos.findIndex(task => task.id === taskId); if (taskIndex === -1) return; const task = storedTodos[taskIndex]; todos.push(task); storedTodos.splice(taskIndex, 1); saveTodos(); saveStoredTodos(); renderTodoList(); renderStorageModal();
        }

        clearTodosBtn.addEventListener('click', openTodoActionModal);

        todoActionModalClearBtn.addEventListener('click', () => {
            let confirmed = false;
            if (todos.length > 0) { if (confirm("确定要清空所有任务吗？" + (isTodoCurtained ? " (同时将解除窗帘模式)" : ""))) { confirmed = true; } }
            else if (isTodoCurtained) { if (confirm("任务列表已空。确定要解除窗帘模式吗？")) { confirmed = true; } }
            else { alert("任务列表已经是空的，也未处于窗帘模式。"); }
            if (confirmed) { performClearAllTodos(); }
            closeTodoActionModal();
        });
        todoActionModalCurtainBtn.addEventListener('click', () => { isTodoCurtained = !isTodoCurtained; saveIsTodoCurtained(); renderTodoList(); closeTodoActionModal(); });
        todoActionModalStorageBtn.addEventListener('click', () => { closeTodoActionModal(); openTodoStorageModal(); });

        function openTodoScheduleModal() {
            if (isTodoCurtained) { alert("请先解除窗帘模式。"); return; }
            todoScheduleModalArrangeBtn.textContent = isArrangeMode ? '✅ 取消安排' : '🗓️ 安排';
            todoScheduleModalResetBtn.disabled = todos.length === 0;
            todoScheduleModal.style.display = 'flex';
        }
        function closeTodoScheduleModal() { todoScheduleModal.style.display = 'none'; }
        resetTodosBtn.addEventListener('click', openTodoScheduleModal);

        todoScheduleModalResetBtn.addEventListener('click', () => {
            if (todos.length > 0 && confirm('确定要重置所有任务到Lv1未完成状态吗？')) {
                todos.forEach(task => { task.currentLevelIndex = 0; task.levels.forEach(level => level.done = false); });
                saveTodos(); renderTodoList(); updateCongratsMessages();
            }
            closeTodoScheduleModal();
        });
        todoScheduleModalArrangeBtn.addEventListener('click', () => {
            const wasArrangeMode = isArrangeMode; isArrangeMode = !isArrangeMode;
            if (!wasArrangeMode && isArrangeMode && todos.length > 0) { arrangeTasksToMorning(); }
            saveIsArrangeMode(); renderTodoList(); updateAnimatedTimeBlock(); closeTodoScheduleModal();
        });

        addTodoBtn.addEventListener('click', addTodo);
        newTodoInput.addEventListener('keypress', (e) => { if (e.key === 'Enter') addTodo(); });

        function openLevelManager(taskId) {
            if (isTodoCurtained) return;
            currentEditingTaskIndex = findTaskIndexById(taskId);
            if (currentEditingTaskIndex === -1) return;
            const task = todos[currentEditingTaskIndex];
            modalTaskTitle.textContent = `管理任务：${escapeHTML(task.levels[0].text.substring(0, 20))}${task.levels[0].text.length > 20 ? '...' : ''}`;
            populateModalLevels(task);
            levelManagerModal.style.display = 'flex';
        }
        function closeLevelManager() { levelManagerModal.style.display = 'none'; currentEditingTaskIndex = -1; modalLevelsList.innerHTML = ''; }
        function populateModalLevels(task) {
            modalLevelsList.innerHTML = '';
            const currentMaxLevel = task.levels.length;
            task.levels.forEach((level, index) => {
                const item = document.createElement('div');
                item.className = 'level-manage-item';
                item.id = `modal-level-${index}`;
                item.innerHTML = `
                    <div class="level-manage-item-display" style="display: flex;">
                        <span class="level-indicator">lv${level.level}</span>
                        <span class="level-text">${escapeHTML(level.text)}</span>
                        <div class="level-manage-controls">
                            <button class="edit-level-btn" onclick="startModalLevelEdit(${currentEditingTaskIndex}, ${index})">编辑</button>
                            <button class="aux-info-btn" onclick="editAuxInfo(${currentEditingTaskIndex}, ${index})">辅助信息</button>
                            ${level.level > 1 ? `<button class="delete-level-btn" onclick="deleteLevel(${currentEditingTaskIndex}, ${index})">删除</button>` : ''}
                        </div>
                    </div>
                    <div class="level-manage-item-edit" style="display: none;">
                        <span class="level-indicator">lv${level.level}</span>
                        <span class="level-text-edit"><input type="text" class="edit-level-input" value="${escapeHTML(level.text)}"></span>
                        <div class="level-manage-controls">
                            <button class="save-level-btn" onclick="saveModalLevelEdit(${currentEditingTaskIndex}, ${index})">保存</button>
                            <button type="button" class="cancel-level-btn" onclick="cancelModalLevelEdit(${currentEditingTaskIndex}, ${index})">取消</button>
                        </div>
                    </div>`;
                modalLevelsList.appendChild(item);
            });
            if (currentMaxLevel < 4) {
                modalAddLevelSection.style.display = 'flex';
                const nextLevel = currentMaxLevel + 1;
                modalNextLevelNum.textContent = nextLevel;
                modalNewLevelText.value = '';
                modalAddLevelBtn.onclick = () => addLevel(currentEditingTaskIndex);
            } else {
                modalAddLevelSection.style.display = 'none';
            }
        }
        function addLevel(taskIndex) {
            if (taskIndex === -1) return;
            const task = todos[taskIndex];
            const newText = modalNewLevelText.value.trim();
            if (!newText) { alert("新层级描述不能为空！"); return; }
            if (task.levels.length >= 4) { alert("最多只能有4个层级！"); return; }
            const nextLevelNum = task.levels.length + 1;
            task.levels.push({ level: nextLevelNum, text: newText, done: false, auxInfo: '' }); // Add auxInfo
            saveTodos(); populateModalLevels(task);
        }
        function startModalLevelEdit(taskIndex, levelIndex) {
            modalLevelsList.querySelectorAll('.level-manage-item').forEach((item, idx) => { if (idx !== levelIndex) { cancelModalLevelEdit(taskIndex, idx, false); } });
            const levelItem = document.getElementById(`modal-level-${levelIndex}`); if (!levelItem) return;
            const displayDiv = levelItem.querySelector('.level-manage-item-display');
            const editDiv = levelItem.querySelector('.level-manage-item-edit');
            const input = editDiv.querySelector('.edit-level-input');
            input.value = todos[taskIndex].levels[levelIndex].text;
            displayDiv.style.display = 'none'; editDiv.style.display = 'flex'; input.focus(); input.select();
        }
        function cancelModalLevelEdit(taskIndex, levelIndex, refreshModal = true) {
            const levelItem = document.getElementById(`modal-level-${levelIndex}`); if (!levelItem) return;
            const displayDiv = levelItem.querySelector('.level-manage-item-display');
            const editDiv = levelItem.querySelector('.level-manage-item-edit');
            if (editDiv.style.display !== 'none') { displayDiv.style.display = 'flex'; editDiv.style.display = 'none'; }
        }
        function saveModalLevelEdit(taskIndex, levelIndex) {
            if (taskIndex === -1 || !todos[taskIndex]?.levels[levelIndex]) return;
            const task = todos[taskIndex];
            const levelItem = document.getElementById(`modal-level-${levelIndex}`); if (!levelItem) return;
            const input = levelItem.querySelector('.edit-level-input');
            const newText = input.value.trim();
            if (!newText) { alert("层级描述不能为空！"); return; }
            task.levels[levelIndex].text = newText;
            saveTodos(); populateModalLevels(task);
        }

        function editAuxInfo(taskIndex, levelIndex) {
            if (taskIndex === -1 || !todos[taskIndex]?.levels[levelIndex]) return;
            const currentInfo = todos[taskIndex].levels[levelIndex].auxInfo || '';
            const newInfo = prompt("请输入辅助信息，会在右键专注模式显示: \n（ 例如:所需时间[15-20, 35+] ）", currentInfo);
            if (newInfo !== null) {
                todos[taskIndex].levels[levelIndex].auxInfo = newInfo.trim();
                saveTodos();
                alert("辅助信息已保存。");
            }
        }

        function deleteLevel(taskIndex, levelIndex) {
            if (taskIndex === -1 || !todos[taskIndex]?.levels[levelIndex]) return;
            const task = todos[taskIndex];
            const level = task.levels[levelIndex];
            if (level.level <= 1) { alert("不能删除 Lv1 任务！"); return; }
            if (confirm(`确定要删除 Lv${level.level}："${level.text}" 吗？`)) {
                task.levels.splice(levelIndex, 1);
                for (let i = levelIndex; i < task.levels.length; i++) { task.levels[i].level = i + 1; }
                if (task.currentLevelIndex >= task.levels.length) { task.currentLevelIndex = task.levels.length - 1; }
                saveTodos(); populateModalLevels(task);
            }
        }
        modalDeleteTaskBtn.addEventListener('click', () => {
            if (currentEditingTaskIndex === -1) return;
            const task = todos[currentEditingTaskIndex];
            if (confirm(`确定要删除整个任务 "${escapeHTML(task.levels[0].text.substring(0, 30))}..." 吗？`)) {
                todos.splice(currentEditingTaskIndex, 1);
                saveTodos(); renderTodoList(); closeLevelManager();
            }
        });

        // --- Habit Tracker ---

        // --- END OF PART 1 ---
        function saveHabits() { localStorage.setItem(LS_HABITS_KEY, JSON.stringify(habits)); }
        function saveStoredHabits() { localStorage.setItem(LS_STORED_HABITS_KEY, JSON.stringify(storedHabits)); }

        function loadStoredHabits() {
            const saved = localStorage.getItem(LS_STORED_HABITS_KEY);
            if (saved) {
                try {
                    storedHabits = JSON.parse(saved);
                    storedHabits.forEach(habit => { if (!habit.id) habit.id = generateId(); });
                } catch (e) {
                    console.error("Error loading stored habits:", e);
                    localStorage.removeItem(LS_STORED_HABITS_KEY);
                    storedHabits = [];
                }
            }
        }

        function renderHabitList() {
            habitList.innerHTML = '';
            const hasHabits = habits.length > 0;
            const todayStr = getLocalDateString();

            if (hasHabits) {
                habits.forEach((habit, index) => {
                    const li = document.createElement('li');
                    li.className = 'habit-item';
                    li.id = `habit-item-${index}`;

                    if (habit.lastIncrementDate === todayStr) li.classList.add('incremented-today');
                    if (habit.isDescriptionFolded === undefined) habit.isDescriptionFolded = false;

                    const descriptionDisplayStyle = habit.isDescriptionFolded ? 'style="display:none;"' : '';
                    const editDescTextareaStyle = habit.isDescriptionFolded ? 'style="display:none;"' : '';
                    const foldButtonText = habit.isDescriptionFolded ? '展开描述' : '折叠描述';
                    const titleClickEvent = isSimplifiedMode ? `onclick="toggleHabitSettings(${index})"` : '';

                    li.innerHTML =
                        `<div class="habit-content">
                            <h4 ${titleClickEvent}>${escapeHTML(habit.title)}</h4>
                            ${habit.desc ? `<p ${descriptionDisplayStyle}>${escapeHTML(habit.desc)}</p>` : ''}
                        </div>
                        <div class="habit-item-edit-form">
                            <input type="text" class="edit-habit-title" value="${escapeHTML(habit.title)}" placeholder="习惯标题">
                            <textarea class="edit-habit-desc" ${editDescTextareaStyle} placeholder="简短描述 (可选)">${escapeHTML(habit.desc)}</textarea>
                            <button class="save-habit-btn" onclick="saveHabitEdit(${index})">保存</button>
                            <button type="button" class="cancel-habit-btn" onclick="cancelHabitEdit(${index})">取消</button>
                        </div>
                        <div class="habit-controls">
                            <div class="habit-counter">
                                <button onclick="decrementHabit(${index})">-</button>
                                <span>${habit.count}</span>
                                <button onclick="incrementHabit(${index})">+</button>
                            </div>
                            <span class="habit-settings-icon" onclick="toggleHabitSettings(${index})" title="设置">⚙️</span>
                        </div>
                        <div class="habit-item-settings">
                            <button class="fold-habit-desc-btn" onclick="toggleHabitDescFold(${index})">${foldButtonText}</button>
                            <button class="edit-habit-btn" onclick="startHabitEdit(${index})">编辑</button>
                            <button class="delete-habit-btn" onclick="deleteHabit(${index})">删除</button>
                        </div>`;
                    habitList.appendChild(li);
                });
            }
            noHabitsMsg.style.display = hasHabits ? 'none' : 'block';
        }
        function addHabit() {
            const title = newHabitTitleInput.value.trim();
            const desc = newHabitDescInput.value.trim();
            if (title) {
                habits.push({ id: generateId(), title, desc, count: 0, isDescriptionFolded: false, lastIncrementDate: null });
                newHabitTitleInput.value = ''; newHabitDescInput.value = '';
                saveHabits(); renderHabitList();
                if (isSimplifiedMode) {
                    const habitInputArea = document.querySelector('#habit-column .input-area');
                    if (habitInputArea) habitInputArea.style.display = 'none';
                }
            }
        }
        function incrementHabit(index) {
            if (habits[index] !== undefined) {
                habits[index].count++;
                habits[index].lastIncrementDate = getLocalDateString();
                const todayStr = getLocalDateString();
                const todayRecord = getOrCreateRecordForDate(todayStr);
                todayRecord.habitsIncremented = (todayRecord.habitsIncremented || 0) + 1;
                saveDailyRecords();
                if (page3.classList.contains('active')) renderRecordsModule();
                saveHabits(); renderHabitList(); triggerAnimation(habitCelebration);
            }
        }
        function decrementHabit(index) {
            if (habits[index] !== undefined) {
                habits[index].count--;
                const todayStr = getLocalDateString();
                const todayRecord = getRecordForDate(todayStr);
                if (todayRecord && todayRecord.habitsIncremented > 0) {
                    todayRecord.habitsIncremented--;
                    saveDailyRecords();
                    if (page3.classList.contains('active')) renderRecordsModule();
                }
                saveHabits(); renderHabitList(); triggerAnimation(habitSadness);
            }
        }
        function deleteHabit(index) { if (habits[index] !== undefined && confirm(`确定要删除习惯 "${habits[index].title}" 吗？`)) { habits.splice(index, 1); saveHabits(); renderHabitList(); } }
        function toggleHabitSettings(index) {
            const habitItem = document.getElementById(`habit-item-${index}`); if (!habitItem) return;
            const settingsPanel = habitItem.querySelector('.habit-item-settings'); if (!settingsPanel) return;
            const isVisible = settingsPanel.classList.contains('show');
            document.querySelectorAll('.habit-item-settings').forEach(panel => { panel.classList.remove('show'); panel.style.display = 'none'; });
            document.querySelectorAll('.habit-item.editing').forEach(item => { if (item.id !== `habit-item-${index}`) cancelHabitEdit(parseInt(item.id.split('-')[2])); });
            if (!isVisible) { settingsPanel.classList.add('show'); }
        }

        function toggleHabitDescFold(index) {
            if (!habits[index]) return;
            habits[index].isDescriptionFolded = !habits[index].isDescriptionFolded;
            saveHabits(); renderHabitList();
        }

        function startHabitEdit(index) {
            const habitItem = document.getElementById(`habit-item-${index}`); if (!habitItem) return;
            const settingsPanel = habitItem.querySelector('.habit-item-settings');
            if (settingsPanel) settingsPanel.style.display = 'none';
            habitItem.classList.add('editing');
            const descTextarea = habitItem.querySelector('.edit-habit-desc');
            if (descTextarea && habits[index]) { descTextarea.style.display = habits[index].isDescriptionFolded ? 'none' : 'block'; }
        }
        function cancelHabitEdit(index) { const habitItem = document.getElementById(`habit-item-${index}`); if (!habitItem) return; habitItem.classList.remove('editing'); }
        function saveHabitEdit(index) {
            const habitItem = document.getElementById(`habit-item-${index}`); if (!habitItem || !habits[index]) return;
            const titleInput = habitItem.querySelector('.edit-habit-title');
            const descInput = habitItem.querySelector('.edit-habit-desc');
            const newTitle = titleInput.value.trim(); const newDesc = descInput.value.trim();
            if (!newTitle) { alert('习惯标题不能为空！'); return; }
            habits[index].title = newTitle; habits[index].desc = newDesc;
            saveHabits(); renderHabitList();
        }
        addHabitBtn.addEventListener('click', addHabit);

        habitAdjustmentBtn.addEventListener('click', () => { habitAdjustmentModal.style.display = 'flex'; renderHabitAdjustmentModal(); });
        function closeHabitAdjustmentModal() { habitAdjustmentModal.style.display = 'none'; }
        function renderHabitAdjustmentModal() {
            adjustmentCurrentHabits.innerHTML = '';
            if (habits.length === 0) { adjustmentCurrentHabits.innerHTML = '<p class="placeholder-msg">暂无习惯</p>'; }
            else { habits.forEach(habit => { const habitDiv = document.createElement('div'); habitDiv.className = 'storage-item'; habitDiv.textContent = habit.title; habitDiv.onclick = () => storeHabit(habit.id); adjustmentCurrentHabits.appendChild(habitDiv); }); }
            adjustmentStoredHabits.innerHTML = '';
            if (storedHabits.length === 0) { adjustmentStoredHabits.innerHTML = '<p class="placeholder-msg">收纳盒为空</p>'; }
            else { storedHabits.forEach(habit => { const habitDiv = document.createElement('div'); habitDiv.className = 'storage-item'; const titleSpan = document.createElement('span'); titleSpan.textContent = habit.title; titleSpan.onclick = () => restoreHabit(habit.id); const deleteBtn = document.createElement('span'); deleteBtn.className = 'delete-stored-habit-btn'; deleteBtn.innerHTML = '×'; deleteBtn.title = '删除此习惯'; deleteBtn.onclick = (e) => { e.stopPropagation(); deleteStoredHabit(habit.id); }; habitDiv.appendChild(titleSpan); habitDiv.appendChild(deleteBtn); adjustmentStoredHabits.appendChild(habitDiv); }); }
        }
        function storeHabit(habitId) {
            const habitIndex = habits.findIndex(h => h.id === habitId); if (habitIndex === -1) return;
            const [habitToStore] = habits.splice(habitIndex, 1);
            storedHabits.push(habitToStore);
            saveHabits(); saveStoredHabits(); renderHabitList(); renderHabitAdjustmentModal();
        }
        function restoreHabit(habitId) {
            const habitIndex = storedHabits.findIndex(h => h.id === habitId); if (habitIndex === -1) return;
            const [habitToRestore] = storedHabits.splice(habitIndex, 1);
            habits.push(habitToRestore);
            saveHabits(); saveStoredHabits(); renderHabitList(); renderHabitAdjustmentModal();
        }
        function deleteStoredHabit(habitId) {
            const habitIndex = storedHabits.findIndex(h => h.id === habitId); if (habitIndex === -1) return;
            if (confirm(`确定要从收纳盒删除习惯 "${storedHabits[habitIndex].title}" 吗？`)) { storedHabits.splice(habitIndex, 1); saveStoredHabits(); renderHabitAdjustmentModal(); }
        }


        // --- Widgets (URL & Status) ---
        function saveUrlSettings(id) { const urlInput = document.getElementById(`url${id}-input`); const emojiInput = document.getElementById(`emoji${id}-input`); const btn = document.getElementById(`url-btn${id}`); const lsKey = id === 1 ? LS_URL1_KEY : LS_URL2_KEY; urlSettings[id].url = urlInput.value.trim(); urlSettings[id].emoji = emojiInput.value.trim() || (id === 1 ? '🔗' : '⛳'); btn.textContent = urlSettings[id].emoji; btn.title = `打开链接${id}` + (urlSettings[id].url ? ` (${urlSettings[id].url})` : ''); localStorage.setItem(lsKey, JSON.stringify(urlSettings[id])); toggleSettings(`url${id}-settings`); }
        function loadUrlSettings() { for (let id = 1; id <= 2; id++) { const lsKey = id === 1 ? LS_URL1_KEY : LS_URL2_KEY; const saved = localStorage.getItem(lsKey); if (saved) { try { urlSettings[id] = JSON.parse(saved); } catch (e) { console.error(`Error parsing URL${id}`); localStorage.removeItem(lsKey); urlSettings[id] = { url: '', emoji: (id === 1 ? '🔗' : '⛳') }; } } else { urlSettings[id] = { url: '', emoji: (id === 1 ? '🔗' : '⛳') }; } const urlInput = document.getElementById(`url${id}-input`); const emojiInput = document.getElementById(`emoji${id}-input`); const btn = document.getElementById(`url-btn${id}`); urlInput.value = urlSettings[id].url; emojiInput.value = urlSettings[id].emoji; btn.textContent = urlSettings[id].emoji; btn.title = `打开链接${id}` + (urlSettings[id].url ? ` (${urlSettings[id].url})` : ''); btn.onclick = () => { if (urlSettings[id].url) { try { window.location.href = urlSettings[id].url; } catch (e) { alert("无法打开链接。请检查链接格式。"); } } else { alert(`链接 ${id} 未设置！请点击右下角 ⚙️ 进行设置。`); toggleSettings(`url${id}-settings`); } }; } }

        function updateStatusButtonsUI() {
            const maxButtons = currentStatusMode === '8-state' ? 8 : 4;
            statusOptionBtns.forEach((btn, index) => {
                if (index < maxButtons) { btn.style.display = 'inline-block'; if (statusData[index]) btn.textContent = statusData[index].name; }
                else { btn.style.display = 'none'; btn.classList.remove('active'); }
            });
            statusOptionsContainer.classList.toggle('eight-states', currentStatusMode === '8-state');
            if (statusContent.style.display === 'flex') { updateStatusQuoteDisplay(); }
        }

        function saveStatusSettings() {
            currentStatusMode = statusModeToggle.checked ? '8-state' : '4-state';
            localStorage.setItem(LS_STATUS_MODE_KEY, currentStatusMode);
            allowMultiStatus = statusMultiSelectToggle.checked;
            localStorage.setItem(LS_STATUS_MULTI_SELECT_KEY, JSON.stringify(allowMultiStatus));
            allowMultiQuotesPerStatus = statusMultiQuoteToggle.checked;
            localStorage.setItem(LS_STATUS_MULTI_QUOTE_MODE_KEY, JSON.stringify(allowMultiQuotesPerStatus));

            const stateCountToSave = currentStatusMode === '8-state' ? 8 : 4;
            while (statusData.length < stateCountToSave) { statusData.push({ name: `状态 ${statusData.length + 1}`, quotes: [`默认鼓励语 ${statusData.length + 1}`] }); }

            for (let i = 0; i < stateCountToSave; i++) {
                const nameInput = document.getElementById(`status${i + 1}-name`);
                if (nameInput) {
                    if (!statusData[i]) statusData[i] = { quotes: [] };
                    statusData[i].name = nameInput.value.trim() || `状态 ${i + 1}`;
                    const quote1Input = document.getElementById(`status${i + 1}-quote1`); const quote2Input = document.getElementById(`status${i + 1}-quote2`); const quote3Input = document.getElementById(`status${i + 1}-quote3`);
                    let currentQuotes = [];
                    if (quote1Input && quote1Input.value.trim()) currentQuotes.push(quote1Input.value.trim());
                    if (allowMultiQuotesPerStatus) {
                        if (quote2Input && quote2Input.value.trim()) currentQuotes.push(quote2Input.value.trim());
                        if (quote3Input && quote3Input.value.trim()) currentQuotes.push(quote3Input.value.trim());
                    }
                    if (currentQuotes.length === 0) { currentQuotes.push(statusData[i].name ? `关于 ${statusData[i].name} 的默认鼓励语` : `默认鼓励语 ${i + 1}`); }
                    statusData[i].quotes = currentQuotes;
                    delete statusData[i].quote;
                }
            }
            localStorage.setItem(LS_STATUS_KEY, JSON.stringify(statusData));
            updateStatusButtonsUI(); updateWidgetColumnLayout(); toggleSettings('status-settings');

            if (!allowMultiStatus) {
                const activeButtons = Array.from(statusOptionBtns).filter(b => b.classList.contains('active'));
                if (activeButtons.length > 1) { activeButtons.slice(1).forEach(b => b.classList.remove('active')); }
            } else {
                const activeButtons = Array.from(statusOptionBtns).filter(b => b.classList.contains('active') && b.style.display !== 'none');
                if (activeButtons.length > 3) { activeButtons.slice(3).forEach(b => b.classList.remove('active')); }
            }
            updateStatusQuoteDisplay();
        }

        const defaultStatusBase = [{ name: "心情一般", quotes: ["玩游戏吧~"] }, { name: "有点累", quotes: ["休息一下吧~"] }, { name: "开心", quotes: ["你可以的！"] }, { name: "身体不适", quotes: ["又活了一天已经很厉害了.jpg"] }];

        function loadStatusSettings() {
            const savedMode = localStorage.getItem(LS_STATUS_MODE_KEY); currentStatusMode = savedMode === '8-state' ? '8-state' : '4-state'; statusModeToggle.checked = currentStatusMode === '8-state';
            const savedMultiSelect = localStorage.getItem(LS_STATUS_MULTI_SELECT_KEY); allowMultiStatus = savedMultiSelect ? JSON.parse(savedMultiSelect) : false; statusMultiSelectToggle.checked = allowMultiStatus;
            const savedMultiQuoteMode = localStorage.getItem(LS_STATUS_MULTI_QUOTE_MODE_KEY); allowMultiQuotesPerStatus = savedMultiQuoteMode ? JSON.parse(savedMultiQuoteMode) : false; statusMultiQuoteToggle.checked = allowMultiQuotesPerStatus;
            const savedData = localStorage.getItem(LS_STATUS_KEY);
            if (savedData) { try { const parsed = JSON.parse(savedData); statusData = Array.isArray(parsed) ? parsed : []; } catch (e) { console.error("Error parsing Status:", e); localStorage.removeItem(LS_STATUS_KEY); statusData = []; } } else { statusData = []; }

            if (Array.isArray(statusData)) {
                statusData.forEach((item, index) => {
                    const baseName = (index < defaultStatusBase.length && defaultStatusBase[index]) ? defaultStatusBase[index].name : `状态 ${index + 1}`;
                    const baseQuotes = (index < defaultStatusBase.length && defaultStatusBase[index] && defaultStatusBase[index].quotes.length > 0) ? defaultStatusBase[index].quotes : [`默认鼓励语 ${index + 1}`];
                    if (typeof item !== 'object' || item === null) { statusData[index] = { name: baseName, quotes: baseQuotes }; item = statusData[index]; }
                    if (typeof item.name !== 'string' || item.name.trim() === '') item.name = baseName;
                    if (typeof item.quote === 'string' && !item.quotes) { item.quotes = [item.quote]; delete item.quote; }
                    if (!Array.isArray(item.quotes) || item.quotes.length === 0) { item.quotes = baseQuotes; }
                });
            } else { statusData = []; }

            const requiredLength = currentStatusMode === '8-state' ? 8 : 4;
            for (let i = 0; i < requiredLength; i++) { if (!statusData[i]) { statusData[i] = (i < defaultStatusBase.length) ? { ...defaultStatusBase[i] } : { name: `状态 ${i + 1}`, quotes: [`默认鼓励语 ${i + 1}`] }; } else { if (!Array.isArray(statusData[i].quotes) || statusData[i].quotes.length === 0) { statusData[i].quotes = (i < defaultStatusBase.length && defaultStatusBase[i].quotes.length > 0) ? [...defaultStatusBase[i].quotes] : [`关于 ${statusData[i].name} 的默认鼓励语`]; } } }
            for (let i = 0; i < 8; i++) {
                const nameInput = document.getElementById(`status${i + 1}-name`); const quote1Input = document.getElementById(`status${i + 1}-quote1`); const quote2Input = document.getElementById(`status${i + 1}-quote2`); const quote3Input = document.getElementById(`status${i + 1}-quote3`);
                if (nameInput && quote1Input) { if (statusData[i]) { nameInput.value = statusData[i].name; quote1Input.value = statusData[i].quotes[0] || ''; if (quote2Input) quote2Input.value = statusData[i].quotes[1] || ''; if (quote3Input) quote3Input.value = statusData[i].quotes[2] || ''; } else { nameInput.value = `状态 ${i + 1}`; quote1Input.value = `默认鼓励语 ${i + 1}`; if (quote2Input) quote2Input.value = ''; if (quote3Input) quote3Input.value = ''; } }
            }

            handleStatusModeToggleUI(currentStatusMode === '8-state'); handleMultiQuoteModeToggleUI(allowMultiQuotesPerStatus); updateStatusButtonsUI(); resetStatusWidgetUI(false); updateWidgetColumnLayout();
        }

        function handleStatusModeToggle(is8StateChecked) {
            handleStatusModeToggleUI(is8StateChecked);
            if (!is8StateChecked) {
                const maxAllowed = allowMultiStatus ? 3 : 1;
                statusOptionBtns.forEach((btn, index) => { if (index >= 4 && btn.classList.contains('active')) { btn.classList.remove('active'); } });
                const visibleActiveButtons = Array.from(statusOptionBtns).filter(b => b.classList.contains('active') && b.style.display !== 'none');
                if (visibleActiveButtons.length > maxAllowed) { visibleActiveButtons.slice(maxAllowed).forEach(b => b.classList.remove('active')); }
            }
            updateStatusQuoteDisplay();
        }
        function handleMultiStatusToggle(isMultiSelectChecked) {
            if (!isMultiSelectChecked) {
                const activeButtons = Array.from(statusOptionBtns).filter(b => b.classList.contains('active'));
                if (activeButtons.length > 1) { activeButtons.slice(1).forEach(b => b.classList.remove('active')); }
            }
            updateStatusQuoteDisplay();
        }
        function handleMultiQuoteModeToggle(isMultiQuoteChecked) {
            allowMultiQuotesPerStatus = isMultiQuoteChecked;
            handleMultiQuoteModeToggleUI(isMultiQuoteChecked);
            updateStatusQuoteDisplay();
        }
        function handleStatusModeToggleUI(is8StateMode) { extraStatusSettingGroups.forEach(group => { group.style.display = is8StateMode ? 'block' : 'none'; }); }
        function handleMultiQuoteModeToggleUI(isMultiQuoteEnabled) { document.querySelectorAll('.status-setting-group').forEach(group => { const extraQuoteDiv = group.querySelector('.multi-quote-extra'); if (extraQuoteDiv) { extraQuoteDiv.style.display = isMultiQuoteEnabled ? 'block' : 'none'; } }); }

        function updateWidgetColumnLayout() {
            const url1Widget = document.getElementById('widget-url1'); const url2Widget = document.getElementById('widget-url2'); const statusWidget = document.getElementById('widget-status'); if (!url1Widget || !url2Widget || !statusWidget) return;
            if (currentStatusMode === '8-state') { url1Widget.style.flexGrow = '0.6'; url1Widget.style.flexShrink = '1'; url1Widget.style.flexBasis = '0%'; url2Widget.style.flexGrow = '0.6'; url2Widget.style.flexShrink = '1'; url2Widget.style.flexBasis = '0%'; statusWidget.style.flexGrow = '2'; statusWidget.style.flexShrink = '1'; statusWidget.style.flexBasis = '0%'; }
            else { url1Widget.style.flexGrow = '1'; url1Widget.style.flexShrink = '1'; url1Widget.style.flexBasis = '0%'; url2Widget.style.flexGrow = '1'; url2Widget.style.flexShrink = '1'; url2Widget.style.flexBasis = '0%'; statusWidget.style.flexGrow = '1'; statusWidget.style.flexShrink = '1'; statusWidget.style.flexBasis = '0%'; }
        }

        function updateStatusQuoteDisplay() {
            const activeQuotesMessages = [];
            statusOptionBtns.forEach(btn => {
                if (btn.classList.contains('active') && btn.style.display !== 'none') {
                    const index = parseInt(btn.dataset.index, 10);
                    if (statusData[index] && statusData[index].quotes && statusData[index].quotes.length > 0) {
                        let quoteToDisplay;
                        if (allowMultiQuotesPerStatus && statusData[index].quotes.length > 0) {
                            if (statusQuoteSelections[index] === undefined) { statusQuoteSelections[index] = Math.floor(Math.random() * statusData[index].quotes.length); }
                            quoteToDisplay = statusData[index].quotes[statusQuoteSelections[index]];
                        } else { quoteToDisplay = statusData[index].quotes[0]; }
                        activeQuotesMessages.push(quoteToDisplay);
                    }
                }
            });
            statusQuote.textContent = activeQuotesMessages.length > 0 ? activeQuotesMessages.join('\n') : '\u00A0';
        }
        function clearStatusQuoteDisplay() { statusQuote.innerHTML = ' '; statusOptionBtns.forEach(btn => btn.classList.remove('active')); statusQuoteSelections = {}; }
        function resetStatusWidgetUI(clearQuote = true) {
            statusTriggerBtn.style.display = 'flex'; statusContent.style.display = 'none';
            if (clearQuote) { clearStatusQuoteDisplay(); } else { updateStatusQuoteDisplay(); }
        }
        statusTriggerBtn.addEventListener('click', () => { statusTriggerBtn.style.display = 'none'; statusContent.style.display = 'flex'; });
        statusOptionBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const currentlyActive = Array.from(statusOptionBtns).filter(b => b.classList.contains('active') && b.style.display !== 'none');
                const btnIndex = parseInt(btn.dataset.index, 10);
                if (allowMultiStatus) {
                    if (btn.classList.contains('active')) { btn.classList.remove('active'); delete statusQuoteSelections[btnIndex]; }
                    else { if (currentlyActive.length < 3) { btn.classList.add('active'); if (allowMultiQuotesPerStatus && statusData[btnIndex] && statusData[btnIndex].quotes && statusData[btnIndex].quotes.length > 1) { statusQuoteSelections[btnIndex] = Math.floor(Math.random() * statusData[btnIndex].quotes.length); } } else { alert("最多只能同时选择3个状态！"); } }
                } else {
                    statusQuoteSelections = {}; statusOptionBtns.forEach(b => b.classList.remove('active')); btn.classList.add('active');
                    if (allowMultiQuotesPerStatus && statusData[btnIndex] && statusData[btnIndex].quotes && statusData[btnIndex].quotes.length > 1) { statusQuoteSelections[btnIndex] = Math.floor(Math.random() * statusData[btnIndex].quotes.length); }
                }
                updateStatusQuoteDisplay();
            });
        });
        statusResetBtn.addEventListener('click', () => { resetStatusWidgetUI(true); });

        // --- Table Module Logic ---
        function saveTablesData() { localStorage.setItem(LS_TABLES_KEY, JSON.stringify(tablesData)); localStorage.setItem(LS_ACTIVE_TABLE_KEY, activeTableId); }
        function loadTablesData() { const savedData = localStorage.getItem(LS_TABLES_KEY); const savedActiveId = localStorage.getItem(LS_ACTIVE_TABLE_KEY); let dataLoaded = false; if (savedData) { try { tablesData = JSON.parse(savedData); if (typeof tablesData !== 'object' || tablesData === null) { throw new Error("Invalid table data format"); } Object.values(tablesData).forEach(table => { if (!table.columnWidths) table.columnWidths = {}; if (!table.headers) table.headers = []; if (!table.rows) table.rows = []; }); dataLoaded = true; } catch (e) { console.error("Error loading tables data:", e); tablesData = {}; dataLoaded = false; } } if (!dataLoaded || Object.keys(tablesData).length === 0) { console.log("No valid tables found or error loading, creating defaults."); tablesData = {}; for (let i = 1; i <= 5; i++) { const newId = generateId(); tablesData[newId] = { name: `表格 ${i}`, headers: [`列 1`, `列 2`], rows: [], columnWidths: {} }; if (i === 1) activeTableId = newId; } saveTablesData(); } else { if (savedActiveId && tablesData[savedActiveId]) { activeTableId = savedActiveId; } else if (Object.keys(tablesData).length > 0) { activeTableId = Object.keys(tablesData)[0]; localStorage.setItem(LS_ACTIVE_TABLE_KEY, activeTableId); } else { activeTableId = null; } } }
        function renderTableModule() { renderTableTabs(); renderCurrentTable(); updateTableNameInput(); updateTableActionButtons(); }
        function renderTableTabs() { tableTabsContainer.innerHTML = ''; if (!tablesData || Object.keys(tablesData).length === 0) return; Object.keys(tablesData).forEach(tabId => { const tabData = tablesData[tabId]; if (!tabData) { console.warn("Skipping potentially corrupted tab data for ID:", tabId); return; } const tabElement = document.createElement('span'); tabElement.className = 'table-tab'; tabElement.dataset.tabId = tabId; tabElement.textContent = escapeHTML(tabData.name || 'Unnamed Tab'); if (tabId === activeTableId) { tabElement.classList.add('active'); } tabElement.onclick = () => switchTableTab(tabId); tableTabsContainer.appendChild(tabElement); }); }
        function switchTableTab(tabId) { if (tabId !== activeTableId && tablesData[tabId]) { activeTableId = tabId; saveTablesData(); renderTableModule(); } }
        function updateTableNameInput() { if (activeTableId && tablesData[activeTableId]) { tableNameInput.value = tablesData[activeTableId].name || ''; tableNameInput.disabled = false; } else { tableNameInput.value = ''; tableNameInput.disabled = true; } }
        tableNameInput.addEventListener('blur', () => { if (activeTableId && tablesData[activeTableId]) { const newName = tableNameInput.value.trim(); if (newName && newName !== tablesData[activeTableId].name) { tablesData[activeTableId].name = newName; saveTablesData(); renderTableTabs(); } else if (!newName) { tableNameInput.value = tablesData[activeTableId].name || ''; } } });
        tableNameInput.addEventListener('keypress', (e) => { if (e.key === 'Enter') { tableNameInput.blur(); } });
        function renderCurrentTable() { tableRenderArea.innerHTML = ''; if (!activeTableId || !tablesData[activeTableId]) { tableRenderArea.innerHTML = '<p class="placeholder-msg">请选择一个表格标签。</p>'; return; } const currentTable = tablesData[activeTableId]; if (!currentTable.headers || !currentTable.rows || !currentTable.columnWidths) { tableRenderArea.innerHTML = '<p class="placeholder-msg">表格数据结构错误。</p>'; console.error("Invalid table structure for ID:", activeTableId, currentTable); return; } const table = document.createElement('table'); const thead = table.createTHead(); const tbody = table.createTBody(); const headerRow = thead.insertRow(); currentTable.headers.forEach((headerText, colIndex) => { const th = document.createElement('th'); th.textContent = headerText; th.setAttribute('contenteditable', 'true'); th.dataset.colIndex = colIndex; if (currentTable.columnWidths[colIndex]) { th.style.width = `${currentTable.columnWidths[colIndex]}px`; } const handle = document.createElement('span'); handle.className = 'resize-handle'; handle.addEventListener('mousedown', (e) => initResize(e, th)); th.appendChild(handle); th.addEventListener('blur', handleHeaderEdit); headerRow.appendChild(th); }); currentTable.rows.forEach((rowData, rowIndex) => { const row = tbody.insertRow(); const cellCount = currentTable.headers.length; for (let colIndex = 0; colIndex < cellCount; colIndex++) { const cell = row.insertCell(); cell.textContent = rowData[colIndex] !== undefined ? rowData[colIndex] : ''; cell.setAttribute('contenteditable', 'true'); cell.dataset.rowIndex = rowIndex; cell.dataset.colIndex = colIndex; cell.addEventListener('blur', handleCellEdit); } }); tableRenderArea.appendChild(table); }
        function handleCellEdit(event) { const cell = event.target; const rowIndex = parseInt(cell.dataset.rowIndex, 10); const colIndex = parseInt(cell.dataset.colIndex, 10); const newText = cell.textContent; if (activeTableId && tablesData[activeTableId] && tablesData[activeTableId].rows[rowIndex] !== undefined && tablesData[activeTableId].rows[rowIndex][colIndex] !== undefined) { if (tablesData[activeTableId].rows[rowIndex][colIndex] !== newText) { tablesData[activeTableId].rows[rowIndex][colIndex] = newText; saveTablesData(); } } else { console.warn("Could not save cell edit, data structure mismatch?", { activeTableId, rowIndex, colIndex }); } }
        function handleHeaderEdit(event) { const th = event.target; const colIndex = parseInt(th.dataset.colIndex, 10); let headerContent = ''; for (let node of th.childNodes) { if (node.nodeType === Node.TEXT_NODE) { headerContent += node.textContent; } } headerContent = headerContent.trim(); if (activeTableId && tablesData[activeTableId] && tablesData[activeTableId].headers[colIndex] !== undefined) { if (tablesData[activeTableId].headers[colIndex] !== headerContent) { if (!headerContent) { th.textContent = tablesData[activeTableId].headers[colIndex]; if (!th.querySelector('.resize-handle')) { const handle = document.createElement('span'); handle.className = 'resize-handle'; handle.addEventListener('mousedown', (e) => initResize(e, th)); th.appendChild(handle); } alert("列标题不能为空！"); } else { tablesData[activeTableId].headers[colIndex] = headerContent; saveTablesData(); } } } else { console.warn("Could not save header edit, data structure mismatch?", { activeTableId, colIndex }); } }
        function updateTableActionButtons() { const hasActiveTable = activeTableId && tablesData[activeTableId]; const currentTable = hasActiveTable ? tablesData[activeTableId] : null; const canDeleteRow = hasActiveTable && currentTable.rows.length > 0; const canDeleteCol = hasActiveTable && currentTable.headers.length > 1; tableAddColBtn.disabled = !hasActiveTable; tableAddRowBtn.disabled = !hasActiveTable; tableDelColBtn.disabled = !canDeleteCol; tableDelRowBtn.disabled = !canDeleteRow; tableClearBtn.disabled = !hasActiveTable; }
        tableAddColBtn.addEventListener('click', () => { if (!activeTableId || !tablesData[activeTableId]) return; const table = tablesData[activeTableId]; const newColIndex = table.headers.length; table.headers.push(`新列`); table.rows.forEach(row => { row.push(''); }); saveTablesData(); renderCurrentTable(); updateTableActionButtons(); });
        tableAddRowBtn.addEventListener('click', () => { if (!activeTableId || !tablesData[activeTableId]) return; const table = tablesData[activeTableId]; const newRow = Array(table.headers.length).fill(''); table.rows.push(newRow); saveTablesData(); renderCurrentTable(); updateTableActionButtons(); });
        tableDelColBtn.addEventListener('click', () => { if (!activeTableId || !tablesData[activeTableId]) return; const table = tablesData[activeTableId]; if (table.headers.length <= 1) { alert("至少需要保留一列！"); return; } const lastColIndex = table.headers.length - 1; if (confirm(`确定要删除最后一列 (${table.headers[lastColIndex]}) 吗？`)) { table.headers.pop(); table.rows.forEach(row => { if (row.length > lastColIndex) { row.pop(); } }); delete table.columnWidths[lastColIndex]; saveTablesData(); renderCurrentTable(); updateTableActionButtons(); } });
        tableDelRowBtn.addEventListener('click', () => { if (!activeTableId || !tablesData[activeTableId]) return; const table = tablesData[activeTableId]; if (table.rows.length === 0) return; if (confirm("确定要删除最后一行数据吗？")) { table.rows.pop(); saveTablesData(); renderCurrentTable(); updateTableActionButtons(); } });
        tableClearBtn.addEventListener('click', () => { if (!activeTableId || !tablesData[activeTableId]) return; const table = tablesData[activeTableId]; if (table.rows.length > 0 && confirm(`确定要清空表格 "${table.name}" 的所有数据行吗？此操作不可撤销。`)) { table.rows = []; saveTablesData(); renderCurrentTable(); updateTableActionButtons(); } else if (table.rows.length === 0) { alert(`表格 "${table.name}" 已经没有数据行了。`); } });
        let resizingColumn = null; let startX = 0; let startWidth = 0;
        function initResize(e, th) { if (e.target.className !== 'resize-handle') return; e.preventDefault(); resizingColumn = th; startX = e.pageX; startWidth = th.offsetWidth; document.addEventListener('mousemove', handleResize); document.addEventListener('mouseup', stopResize); document.body.style.userSelect = 'none'; document.body.style.cursor = 'col-resize'; }
        function handleResize(e) { if (!resizingColumn) return; const currentX = e.pageX; const diffX = currentX - startX; const newWidth = Math.max(50, startWidth + diffX); resizingColumn.style.width = `${newWidth}px`; }
        function stopResize(e) { if (!resizingColumn) return; const finalWidth = resizingColumn.offsetWidth; const colIndex = parseInt(resizingColumn.dataset.colIndex, 10); if (activeTableId && tablesData[activeTableId] && tablesData[activeTableId].headers[colIndex] !== undefined) { if (!tablesData[activeTableId].columnWidths) tablesData[activeTableId].columnWidths = {}; tablesData[activeTableId].columnWidths[colIndex] = finalWidth; saveTablesData(); } resizingColumn = null; document.removeEventListener('mousemove', handleResize); document.removeEventListener('mouseup', stopResize); document.body.style.userSelect = ''; document.body.style.cursor = ''; }

        // --- Q&A Module Logic ---
        function saveQAData() { localStorage.setItem(LS_QA_KEY, JSON.stringify(qaData)); localStorage.setItem(LS_QA_FOLDED_KEY, JSON.stringify(isQAFolded)); }
        function saveQAGroups() { localStorage.setItem(LS_QA_GROUPS_KEY, JSON.stringify(qaGroups)); }
        function loadQAData() { const savedQA = localStorage.getItem(LS_QA_KEY); const savedFolded = localStorage.getItem(LS_QA_FOLDED_KEY); if (savedQA) { try { const parsedQA = JSON.parse(savedQA); if (Array.isArray(parsedQA)) { qaData = parsedQA.map(item => ({ id: item.id || generateId(), question: item.question || '', answer: item.answer || '' })); } else { throw new Error("Invalid QA data format"); } } catch (e) { console.error("Error loading QA data:", e); localStorage.removeItem(LS_QA_KEY); qaData = []; } } else { qaData = []; } if (savedFolded) { try { isQAFolded = JSON.parse(savedFolded); } catch (e) { console.error("Error loading QA folded state:", e); isQAFolded = false; } } else { isQAFolded = false; } }
        function loadQAGroups() { const savedGroups = localStorage.getItem(LS_QA_GROUPS_KEY); if (savedGroups) { try { const parsedGroups = JSON.parse(savedGroups); if (Array.isArray(parsedGroups)) { qaGroups = parsedGroups.map(group => ({ id: group.id || generateId(), name: group.name || '未命名问题组', questions: Array.isArray(group.questions) ? group.questions : [], createdAt: group.createdAt || new Date().toISOString() })); } else { throw new Error("Invalid QA groups data format"); } } catch (e) { console.error("Error loading QA groups data:", e); localStorage.removeItem(LS_QA_GROUPS_KEY); qaGroups = []; } } else { qaGroups = []; } }
        function autoResizeTextarea(textarea) { textarea.style.height = 'auto'; textarea.style.height = textarea.scrollHeight + 'px'; }
        function renderQAModule() {
            if (isQAFolded) { qaContentArea.style.display = 'none'; qaFoldedPlaceholder.style.display = 'flex'; qaToggleFoldBtn.textContent = '展开'; qaAddBtn.disabled = true; qaEditBtn.disabled = true; qaClearAnswersBtn.disabled = true; qaGroupsBtn.disabled = true; }
            else { qaContentArea.style.display = 'flex'; qaFoldedPlaceholder.style.display = 'none'; qaToggleFoldBtn.textContent = '折叠'; const hasQA = qaData.length > 0; qaAddBtn.disabled = false; qaEditBtn.disabled = !hasQA; qaClearAnswersBtn.disabled = !hasQA; qaGroupsBtn.disabled = false; }
            qaToggleFoldBtn.disabled = false; qaContentArea.innerHTML = '';
            if (!isQAFolded) {
                if (qaData.length === 0) { qaContentArea.innerHTML = '<p class="placeholder-msg">点击 "添加提问" 开始</p>'; }
                else { qaData.forEach(item => { const pairDiv = document.createElement('div'); pairDiv.className = 'qa-pair'; pairDiv.dataset.id = item.id; const questionDiv = document.createElement('div'); questionDiv.className = 'qa-question'; questionDiv.textContent = item.question; const answerDiv = document.createElement('div'); answerDiv.className = 'qa-answer'; answerDiv.textContent = item.answer; answerDiv.dataset.id = item.id; answerDiv.onclick = () => openAnswerModal(item.id); pairDiv.appendChild(questionDiv); pairDiv.appendChild(answerDiv); qaContentArea.appendChild(pairDiv); }); }
            }
        }
        function addQuestion() { const questionText = prompt("请输入你的提问："); if (questionText && questionText.trim() !== '') { const newId = generateId(); qaData.push({ id: newId, question: questionText.trim(), answer: '' }); saveQAData(); renderQAModule(); qaContentArea.scrollTop = qaContentArea.scrollHeight; } }
        function clearAllAnswers() { if (qaData.length === 0) return; if (confirm("确定要清空所有回答吗？提问将保留。")) { qaData.forEach(item => item.answer = ''); saveQAData(); renderQAModule(); } }
        function toggleQAFold() { isQAFolded = !isQAFolded; saveQAData(); renderQAModule(); }
        function openQAEditorModal() { if (qaData.length === 0) { alert("没有可编辑的提问。"); return; } populateQAEditorModal(); qaEditorModal.style.display = 'flex'; }
        function closeQAEditorModal() { qaEditorModal.style.display = 'none'; qaEditorList.innerHTML = ''; renderQAModule(); }
        function populateQAEditorModal() { qaEditorList.innerHTML = ''; if (qaData.length === 0) { qaEditorList.innerHTML = '<p class="placeholder-msg">没有提问可编辑。</p>'; return; } qaData.forEach(item => { const editItemDiv = document.createElement('div'); editItemDiv.className = 'qa-edit-item'; const textSpan = document.createElement('span'); textSpan.className = 'qa-edit-item-text'; textSpan.textContent = item.question; textSpan.title = item.question; const controlsDiv = document.createElement('div'); controlsDiv.className = 'qa-edit-item-controls'; const editBtn = document.createElement('button'); editBtn.className = 'edit-qa-btn'; editBtn.textContent = '编辑'; editBtn.onclick = () => startQAEdit(item.id); const deleteBtn = document.createElement('button'); deleteBtn.className = 'delete-qa-btn'; deleteBtn.textContent = '删除'; deleteBtn.onclick = () => deleteQuestion(item.id); controlsDiv.appendChild(editBtn); controlsDiv.appendChild(deleteBtn); editItemDiv.appendChild(textSpan); editItemDiv.appendChild(controlsDiv); qaEditorList.appendChild(editItemDiv); }); }
        function startQAEdit(id) { const itemIndex = qaData.findIndex(item => item.id === id); if (itemIndex === -1) return; const currentQuestion = qaData[itemIndex].question; const newQuestion = prompt("编辑提问：", currentQuestion); if (newQuestion !== null && newQuestion.trim() !== '' && newQuestion.trim() !== currentQuestion) { qaData[itemIndex].question = newQuestion.trim(); saveQAData(); populateQAEditorModal(); } else if (newQuestion !== null && newQuestion.trim() === '') { alert("提问内容不能为空！"); } }
        function deleteQuestion(id) { const itemIndex = qaData.findIndex(item => item.id === id); if (itemIndex === -1) return; const questionText = qaData[itemIndex].question; if (confirm(`确定要删除提问 "${questionText.substring(0, 30)}..." 及其对应的回答吗？`)) { qaData.splice(itemIndex, 1); saveQAData(); populateQAEditorModal(); } }
        function openAnswerModal(qaItemId) {
            currentEditingQAItemId = qaItemId;
            const qaItem = qaData.find(item => item.id === qaItemId); if (!qaItem) { console.error("QA item not found for ID:", qaItemId); return; }
            qaAnswerModalQuestion.textContent = qaItem.question; qaAnswerModalInput.value = qaItem.answer; qaAnswerModalInput.removeEventListener('input', handleModalTextareaInput); qaAnswerModalInput.addEventListener('input', handleModalTextareaInput); qaAnswerModal.style.display = 'flex'; autoResizeTextarea(qaAnswerModalInput); qaAnswerModalInput.focus();
        }
        function handleModalTextareaInput() { autoResizeTextarea(this); }
        function closeAnswerModal() { qaAnswerModal.style.display = 'none'; qaAnswerModalInput.value = ''; currentEditingQAItemId = null; }
        function saveAnswerFromModal() {
            if (!currentEditingQAItemId) return;
            const qaItem = qaData.find(item => item.id === currentEditingQAItemId); if (!qaItem) { console.error("QA item not found for saving, ID:", currentEditingQAItemId); closeAnswerModal(); return; }
            qaItem.answer = qaAnswerModalInput.value;
            saveQAData(); renderQAModule(); closeAnswerModal();
        }

        // --- QA Groups Management Functions ---
        function openQAGroupsModal() {
            renderQAGroupsList();
            renderQACurrentPreview();
            qaGroupsModal.style.display = 'flex';
        }
        function closeQAGroupsModal() {
            qaGroupsModal.style.display = 'none';
            qaGroupNameInput.value = '';
        }
        function renderQAGroupsList() {
            qaGroupsList.innerHTML = '';
            if (qaGroups.length === 0) {
                qaGroupsList.innerHTML = '<p class="placeholder-msg">暂无保存的问题组</p>';
                return;
            }
            qaGroups.forEach(group => {
                const groupDiv = document.createElement('div');
                groupDiv.className = 'qa-group-item';
                groupDiv.style.cssText = 'padding: 8px; margin-bottom: 5px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; background-color: white;';
                groupDiv.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>${escapeHTML(group.name)}</strong>
                            <div style="font-size: 12px; color: #666;">${group.questions.length} 个问题</div>
                        </div>
                        <div>
                            <button onclick="loadQAGroup('${group.id}')" class="std-btn" style="font-size: 12px; padding: 2px 6px; margin-right: 3px; background-color: #007bff;">加载</button>
                            <button onclick="deleteQAGroup('${group.id}')" class="std-btn" style="font-size: 12px; padding: 2px 6px; background-color: #dc3545;">删除</button>
                        </div>
                    </div>
                `;
                qaGroupsList.appendChild(groupDiv);
            });
        }
        function renderQACurrentPreview() {
            qaCurrentPreview.innerHTML = '';
            if (qaData.length === 0) {
                qaCurrentPreview.innerHTML = '<p class="placeholder-msg">当前没有问题</p>';
                return;
            }
            qaData.forEach((item, index) => {
                const previewDiv = document.createElement('div');
                previewDiv.style.cssText = 'padding: 5px; margin-bottom: 3px; border: 1px solid #ccc; border-radius: 3px; background-color: white; font-size: 13px;';
                previewDiv.innerHTML = `<strong>Q${index + 1}:</strong> ${escapeHTML(item.question.substring(0, 50))}${item.question.length > 50 ? '...' : ''}`;
                qaCurrentPreview.appendChild(previewDiv);
            });
        }
        function saveCurrentQAGroup() {
            const groupName = qaGroupNameInput.value.trim();
            if (!groupName) {
                alert('请输入问题组名称！');
                return;
            }
            if (qaData.length === 0) {
                alert('当前没有问题可以保存！');
                return;
            }
            const newGroup = {
                id: generateId(),
                name: groupName,
                questions: qaData.map(item => ({ ...item })), // 深拷贝当前问题
                createdAt: new Date().toISOString()
            };
            qaGroups.push(newGroup);
            saveQAGroups();
            renderQAGroupsList();
            qaGroupNameInput.value = '';
            alert(`问题组 "${groupName}" 保存成功！`);
        }
        function loadQAGroup(groupId) {
            const group = qaGroups.find(g => g.id === groupId);
            if (!group) {
                alert('问题组不存在！');
                return;
            }
            if (confirm(`确定要加载问题组 "${group.name}" 吗？这将替换当前的所有问题。`)) {
                qaData = group.questions.map(item => ({ ...item, id: generateId() })); // 重新生成ID避免冲突
                saveQAData();
                renderQAModule();
                renderQACurrentPreview();
                alert(`问题组 "${group.name}" 加载成功！`);
            }
        }
        function deleteQAGroup(groupId) {
            const group = qaGroups.find(g => g.id === groupId);
            if (!group) {
                alert('问题组不存在！');
                return;
            }
            if (confirm(`确定要删除问题组 "${group.name}" 吗？此操作不可撤销。`)) {
                qaGroups = qaGroups.filter(g => g.id !== groupId);
                saveQAGroups();
                renderQAGroupsList();
                alert(`问题组 "${group.name}" 删除成功！`);
            }
        }
        qaAddBtn.addEventListener('click', addQuestion); qaEditBtn.addEventListener('click', openQAEditorModal); qaClearAnswersBtn.addEventListener('click', clearAllAnswers); qaGroupsBtn.addEventListener('click', openQAGroupsModal); qaToggleFoldBtn.addEventListener('click', toggleQAFold); qaAnswerModalSaveBtn.addEventListener('click', saveAnswerFromModal); qaSaveGroupBtn.addEventListener('click', saveCurrentQAGroup);

        // --- Import/Export Functions ---
        function exportData() {
            tableNameInput.blur();
            const data = {
                exportDate: new Date().toISOString(), todos: todos, isTodoCurtained: isTodoCurtained, isArrangeMode: isArrangeMode, timeBlockRainbowStates: timeBlockRainbowStates, storedTodos: storedTodos, isFirstTaskAdded: isFirstTaskAdded, habits: habits, storedHabits: storedHabits, urlSettings: urlSettings, statusData: statusData, currentStatusMode: currentStatusMode, allowMultiStatus: allowMultiStatus, allowMultiQuotesPerStatus: allowMultiQuotesPerStatus, tablesData: tablesData, activeTableId: activeTableId, qaData: qaData, qaGroups: qaGroups, isQAFolded: isQAFolded, todaySectionData: todaySectionData, streakData: streakData, dailyRecords: dailyRecords
            };
            const jsonString = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' }); const url = URL.createObjectURL(blob);
            const a = document.createElement('a'); a.href = url; a.download = `gotodo_backup_${new Date().toISOString().slice(0, 10)}.json`; document.body.appendChild(a); a.click(); setTimeout(() => { document.body.removeChild(a); URL.revokeObjectURL(url); }, 100);
        }
        function selectImportFile() { importFileInput.click(); }
        function handleFileSelect(event) {
            const file = event.target.files[0]; if (!file) return; importFileName.textContent = file.name; importFileDate.textContent = new Date(file.lastModified).toLocaleString(); const reader = new FileReader(); reader.onload = function (e) {
                try {
                    importData = JSON.parse(e.target.result); if (!importData || typeof importData !== 'object') throw new Error("无效的文件内容"); if (!importData.exportDate) throw new Error("缺少 exportDate 字段");
                    if (importData.todos !== undefined && !Array.isArray(importData.todos)) throw new Error("任务列表格式错误");
                    if (importData.habits !== undefined && !Array.isArray(importData.habits)) throw new Error("习惯列表格式错误");
                    if (importData.statusData !== undefined && !Array.isArray(importData.statusData)) throw new Error("状态数据格式错误");
                    importModal.style.display = 'flex';
                } catch (error) { alert("导入文件解析失败：" + error.message); console.error("Import Error:", error); importData = null; closeImportModal(); }
            }; reader.onerror = function () { alert("读取文件时出错。"); importData = null; closeImportModal(); }; reader.readAsText(file);
        }
        function closeImportModal() { importModal.style.display = 'none'; importFileInput.value = ''; importData = null; importFileName.textContent = '未选择文件'; importFileDate.textContent = '-'; importTodosCheck.checked = true; importHabitsCheck.checked = true; importUrlSettingsCheck.checked = true; importStatusSettingsCheck.checked = true; importTablesCheck.checked = true; importQACheck.checked = true; importTodayStreakCheck.checked = true; importDailyRecordsCheck.checked = true; }
        function confirmImport() {
            if (!importData) { alert("没有有效的导入数据"); return; }
            let importedSomething = false;
            if (importTodosCheck.checked) {
                if (Array.isArray(importData.todos)) {
                    todos = importData.todos;
                    todos.forEach(task => { if (task.levels && Array.isArray(task.levels)) { task.levels.forEach(level => { if (level.auxInfo === undefined) { level.auxInfo = ''; } }); } });
                    saveTodos(); importedSomething = true;
                }
                if (typeof importData.isTodoCurtained === 'boolean') { isTodoCurtained = importData.isTodoCurtained; saveIsTodoCurtained(); importedSomething = true; }
                if (typeof importData.isArrangeMode === 'boolean') { isArrangeMode = importData.isArrangeMode; saveIsArrangeMode(); importedSomething = true; }
                if (typeof importData.timeBlockRainbowStates === 'object' && importData.timeBlockRainbowStates !== null) { timeBlockRainbowStates = importData.timeBlockRainbowStates; saveTimeBlockRainbowStates(); importedSomething = true; }
                if (Array.isArray(importData.storedTodos)) { storedTodos = importData.storedTodos; saveStoredTodos(); importedSomething = true; }
                if (typeof importData.isFirstTaskAdded === 'boolean') { isFirstTaskAdded = importData.isFirstTaskAdded; saveFirstTaskAdded(); importedSomething = true; }
            }
            if (importHabitsCheck.checked) {
                if (Array.isArray(importData.habits)) { habits = importData.habits.map(h => { let isFolded = false; if (h.descFoldedInEdit !== undefined) isFolded = h.descFoldedInEdit; else if (h.isDescriptionFolded !== undefined) isFolded = h.isDescriptionFolded; return { ...h, id: h.id || generateId(), isDescriptionFolded: isFolded, lastIncrementDate: h.lastIncrementDate || null, descFoldedInEdit: undefined }; }); saveHabits(); importedSomething = true; }
                if (Array.isArray(importData.storedHabits)) { storedHabits = importData.storedHabits.map(h => ({ ...h, id: h.id || generateId() })); saveStoredHabits(); importedSomething = true; }
            }
            if (importUrlSettingsCheck.checked && typeof importData.urlSettings === 'object' && importData.urlSettings !== null) { urlSettings = importData.urlSettings; localStorage.setItem(LS_URL1_KEY, JSON.stringify(urlSettings[1] || { url: '', emoji: '🔗' })); localStorage.setItem(LS_URL2_KEY, JSON.stringify(urlSettings[2] || { url: '', emoji: '⛳' })); importedSomething = true; }
            if (importStatusSettingsCheck.checked) {
                if (Array.isArray(importData.statusData)) { statusData = importData.statusData.map((item, idx) => { const baseName = (idx < defaultStatusBase.length && defaultStatusBase[idx]) ? defaultStatusBase[idx].name : `状态 ${idx + 1}`; const baseQuotes = (idx < defaultStatusBase.length && defaultStatusBase[idx] && defaultStatusBase[idx].quotes.length > 0) ? defaultStatusBase[idx].quotes : [`默认鼓励语 ${idx + 1}`]; let currentName = (item && typeof item.name === 'string' && item.name.trim() !== '') ? item.name.trim() : baseName; let currentQuotes = baseQuotes; if (item && typeof item.quote === 'string' && !item.quotes) { currentQuotes = [item.quote]; } else if (item && Array.isArray(item.quotes) && item.quotes.length > 0) { currentQuotes = item.quotes.map(q => String(q).trim()).filter(q => q); if (currentQuotes.length === 0) currentQuotes = baseQuotes; } else if (item && !Array.isArray(item.quotes) && item.quotes) { currentQuotes = [String(item.quotes)]; } return { name: currentName, quotes: currentQuotes }; }); localStorage.setItem(LS_STATUS_KEY, JSON.stringify(statusData)); importedSomething = true; }
                if (importData.currentStatusMode && ['4-state', '8-state'].includes(importData.currentStatusMode)) { currentStatusMode = importData.currentStatusMode; localStorage.setItem(LS_STATUS_MODE_KEY, currentStatusMode); importedSomething = true; }
                if (typeof importData.allowMultiStatus === 'boolean') { allowMultiStatus = importData.allowMultiStatus; localStorage.setItem(LS_STATUS_MULTI_SELECT_KEY, JSON.stringify(allowMultiStatus)); importedSomething = true; }
                if (typeof importData.allowMultiQuotesPerStatus === 'boolean') { allowMultiQuotesPerStatus = importData.allowMultiQuotesPerStatus; localStorage.setItem(LS_STATUS_MULTI_QUOTE_MODE_KEY, JSON.stringify(allowMultiQuotesPerStatus)); importedSomething = true; }
            }
            if (importTablesCheck.checked && typeof importData.tablesData === 'object' && importData.tablesData !== null) { tablesData = importData.tablesData; Object.values(tablesData).forEach(table => { table.name = table.name || '导入表格'; table.headers = table.headers || []; table.rows = table.rows || []; table.columnWidths = table.columnWidths || {}; }); activeTableId = (importData.activeTableId && tablesData[importData.activeTableId]) ? importData.activeTableId : (Object.keys(tablesData).length > 0 ? Object.keys(tablesData)[0] : null); saveTablesData(); importedSomething = true; }
            if (importQACheck.checked && Array.isArray(importData.qaData)) { qaData = importData.qaData; qaData.forEach(item => { item.id = item.id || generateId(); }); isQAFolded = typeof importData.isQAFolded === 'boolean' ? importData.isQAFolded : false; if (Array.isArray(importData.qaGroups)) { qaGroups = importData.qaGroups; qaGroups.forEach(group => { group.id = group.id || generateId(); }); saveQAGroups(); } saveQAData(); importedSomething = true; }
            if (importTodayStreakCheck.checked) { if (typeof importData.todaySectionData === 'object' && importData.todaySectionData !== null) { todaySectionData = importData.todaySectionData; if (!todaySectionData.moodIcons) { todaySectionData.moodIcons = { angry: '😡', neutral: '😑', hurt: '🤕' }; } saveTodayData(); importedSomething = true; } if (typeof importData.streakData === 'object' && importData.streakData !== null) { streakData = importData.streakData; saveStreakData(); importedSomething = true; } }
            if (importDailyRecordsCheck.checked && Array.isArray(importData.dailyRecords)) { dailyRecords = importData.dailyRecords; dailyRecords.forEach(r => { if (!r.moods) r.moods = { angry: 0, neutral: 0, hurt: 0 }; }); saveDailyRecords(); importedSomething = true; }
            if (importedSomething) { initializeAppUIUpdates(); alert("数据导入成功！"); } else { alert("未选择任何数据进行导入或所选数据不存在于文件中。"); }
            closeImportModal();
        }
        function initializeAppUIUpdates() {
            loadUrlSettings(); loadStatusSettings(); renderTodoList(); renderHabitList(); renderTableModule(); renderQAModule(); renderTodayLeftPanel(); renderStreakModule(); renderRecordsModule(); dailyStreakCheck(); updateHideIntroButtonVisibility(); const currentPage = localStorage.getItem(LS_PAGE_KEY) || 'page1'; navigateTo(currentPage);
        }
        exportDataBtn.addEventListener('click', exportData); importDataBtn.addEventListener('click', selectImportFile); importFileInput.addEventListener('change', handleFileSelect);
        hideIntroBtn.addEventListener('click', () => { isIntroHidden = true; saveIsIntroHidden(); updateIntroVisibility(); navigateTo('page2'); });
        showIntroBtn.addEventListener('click', () => { isIntroHidden = false; saveIsIntroHidden(); updateIntroVisibility(); });
        exportDataP3Btn.addEventListener('click', exportData); importDataP3Btn.addEventListener('click', selectImportFile);
        simplifyModeBtn.addEventListener('click', toggleSimplifiedMode);
        // 优化：缓存DOM元素，减少查询次数
        let cachedTimeBlockDividers = null;
        let lastTimeBlockIdx = -1;

        function updateAnimatedTimeBlock() {
            if (!isArrangeMode || !todoList) return;

            logPerformance('timerCalls');
            const currentTimeBlockIdx = getCurrentTimeBlockIndex();

            // 只在时间块改变时才更新
            if (currentTimeBlockIdx === lastTimeBlockIdx) return;
            lastTimeBlockIdx = currentTimeBlockIdx;

            // 缓存DOM查询结果
            if (!cachedTimeBlockDividers) {
                logPerformance('domQueries');
                cachedTimeBlockDividers = todoList.querySelectorAll('.time-block-divider');
            }

            cachedTimeBlockDividers.forEach(divider => {
                const dividerTimeBlockIndex = parseInt(divider.dataset.timeBlockIndex, 10);
                if (dividerTimeBlockIndex === currentTimeBlockIdx) {
                    divider.classList.add('current-time-block');
                } else {
                    divider.classList.remove('current-time-block');
                }
                if (timeBlockRainbowStates[dividerTimeBlockIndex]) {
                    divider.classList.add('rainbow-mode');
                } else {
                    divider.classList.remove('rainbow-mode');
                }
            });
            updateTimeBlockTasksBlur();
        }

        // 清除缓存的函数
        function clearTimeBlockCache() {
            cachedTimeBlockDividers = null;
            lastTimeBlockIdx = -1;
        }
        // 优化：缓存任务元素，减少DOM查询
        let cachedTaskItems = null;

        function updateTimeBlockTasksBlur() {
            if (!isArrangeMode || !todoList) return;

            // 缓存任务元素
            if (!cachedTaskItems) {
                logPerformance('domQueries');
                cachedTaskItems = todoList.querySelectorAll('.todo-item:not(.time-block-divider)');
            }

            // 批量移除类名
            cachedTaskItems.forEach(taskItem => {
                taskItem.classList.remove('rainbow-blur');
            });

            // 优化：只处理激活的彩虹状态
            const activeRainbowBlocks = Object.keys(timeBlockRainbowStates).filter(key => timeBlockRainbowStates[key] === true);

            if (activeRainbowBlocks.length > 0) {
                activeRainbowBlocks.forEach(timeBlockIndex => {
                    const blockIndex = parseInt(timeBlockIndex, 10);
                    todos.forEach(task => {
                        if (task.timeBlock === blockIndex) {
                            const taskElement = document.getElementById(`task-${task.id}`);
                            if (taskElement) {
                                taskElement.classList.add('rainbow-blur');
                            }
                        }
                    });
                });
            }
        }

        // 清除任务缓存的函数
        function clearTaskItemsCache() {
            cachedTaskItems = null;
        }

        // 定时器管理函数 - 移到全局作用域
        let animationTimer = null;

        function startAnimationTimer() {
            if (animationTimer) clearInterval(animationTimer);
            if (isArrangeMode) {
                animationTimer = setInterval(updateAnimatedTimeBlock, 300000); // 改为5分钟执行一次
            }
        }

        function stopAnimationTimer() {
            if (animationTimer) {
                clearInterval(animationTimer);
                animationTimer = null;
            }
        }

        // UI缓存管理函数 - 移到全局作用域
        let cachedSettingsPanels = null;
        let cachedHabitSettings = null;

        function getCachedSettingsPanels() {
            if (!cachedSettingsPanels) {
                logPerformance('domQueries');
                cachedSettingsPanels = document.querySelectorAll('.settings-panel');
            }
            return cachedSettingsPanels;
        }

        function getCachedHabitSettings() {
            if (!cachedHabitSettings) {
                logPerformance('domQueries');
                cachedHabitSettings = document.querySelectorAll('.habit-item-settings.show');
            }
            return cachedHabitSettings;
        }

        function clearUICache() {
            cachedSettingsPanels = null;
            cachedHabitSettings = null;
        }

        // --- Initial Load ---
        function initializeApp() {
            const savedTodos = localStorage.getItem(LS_TODOS_KEY); const savedHabits = localStorage.getItem(LS_HABITS_KEY);
            if (savedTodos) { try { todos = JSON.parse(savedTodos); todos.forEach(task => { if (task.levels && Array.isArray(task.levels)) { task.levels.forEach(level => { if (level.auxInfo === undefined) { level.auxInfo = ''; } }); } }); } catch (e) { console.error("Err Todos", e); localStorage.removeItem(LS_TODOS_KEY); todos = []; } }
            if (savedHabits) { try { habits = JSON.parse(savedHabits); habits.forEach(h => { if (!h.id) h.id = generateId(); if (h.descFoldedInEdit !== undefined) { h.isDescriptionFolded = h.descFoldedInEdit; delete h.descFoldedInEdit; } else if (h.isDescriptionFolded === undefined) { h.isDescriptionFolded = false; } if (h.lastIncrementDate === undefined) { h.lastIncrementDate = null; } }); } catch (e) { console.error("Err Habits", e); localStorage.removeItem(LS_HABITS_KEY); habits = []; } } else { habits = []; }
            loadUrlSettings(); loadStatusSettings(); loadTablesData(); loadQAData(); loadQAGroups(); loadTodayData(); loadStreakData(); loadDailyRecords(); loadIsTodoCurtained(); loadIsArrangeMode(); loadIsIntroHidden(); loadSimplifiedMode(); loadStoredTodos(); loadStoredHabits(); loadFirstTaskAdded(); loadTimeBlockRainbowStates(); dailyStreakCheck(); updateHideIntroButtonVisibility();
            todoFocusOverlay = document.getElementById('todo-focus-overlay'); if (todoFocusOverlay) { todoFocusOverlay.addEventListener('click', exitFocusMode); }
            const lastPage = localStorage.getItem(LS_PAGE_KEY) || 'page1'; navigateTo(lastPage);
            const todoColumnHeader = document.querySelector('#todo-column h2'); const habitColumnHeader = document.querySelector('#habit-column h2');
            if (todoColumnHeader) { todoColumnHeader.addEventListener('click', () => toggleInputArea('todo-column')); }
            if (habitColumnHeader) { habitColumnHeader.addEventListener('click', () => toggleInputArea('habit-column')); }
            // 启动定时器
            startAnimationTimer();
            // 优化：使用事件委托和缓存，减少DOM查询

            window.onclick = function (event) {
                // 模态框关闭检查 - 优化：使用Map减少比较次数
                const modalCloseMap = new Map([
                    [levelManagerModal, closeLevelManager],
                    [importModal, closeImportModal],
                    [qaEditorModal, closeQAEditorModal],
                    [qaAnswerModal, closeAnswerModal],
                    [qaGroupsModal, closeQAGroupsModal],
                    [addChallengeModal, closeAddChallengeModal],
                    [streakTaskDetailsModal, closeStreakDetailsModal],
                    [todoActionModal, closeTodoActionModal],
                    [todoScheduleModal, closeTodoScheduleModal],
                    [todoStorageModal, closeTodoStorageModal],
                    [habitAdjustmentModal, closeHabitAdjustmentModal],
                    [moodCustomizationModal, closeMoodCustomizationModal]
                ]);

                const closeFunction = modalCloseMap.get(event.target);
                if (closeFunction) closeFunction();

                // 设置面板关闭检查 - 优化：减少closest调用
                const target = event.target;
                const isSettingsRelated = target.closest('.settings-panel') ||
                                        target.closest('.settings-icon') ||
                                        target.closest('.habit-settings-icon');

                if (!isSettingsRelated) {
                    getCachedSettingsPanels().forEach(p => p.style.display = 'none');
                    clearUICache(); // 清除缓存，因为DOM可能已改变
                }

                // 习惯设置关闭检查
                const isHabitSettingsRelated = target.closest('.habit-item-settings') ||
                                             target.closest('.habit-settings-icon') ||
                                             target.closest('.habit-item h4');

                if (!isHabitSettingsRelated) {
                    getCachedHabitSettings().forEach(p => {
                        if (!isSimplifiedMode || (isSimplifiedMode && !target.closest('.habit-item h4'))) {
                            p.classList.remove('show');
                        }
                    });
                    clearUICache(); // 清除缓存
                }
            }
        }

        // --- Today Module ---
        function saveTodayData() { localStorage.setItem(LS_TODAY_DATA_KEY, JSON.stringify(todaySectionData)); }
        function loadTodayData() {
            const saved = localStorage.getItem(LS_TODAY_DATA_KEY); const defaultIcons = { angry: '😡', neutral: '😑', hurt: '🤕' };
            if (saved) { try { const parsed = JSON.parse(saved); if (typeof parsed.rating === 'number' && typeof parsed.moods === 'object' && parsed.moods.angry !== undefined) { todaySectionData = parsed; if (!todaySectionData.moodIcons) { todaySectionData.moodIcons = defaultIcons; } if (!todaySectionData.moodClickCounts) { todaySectionData.moodClickCounts = { angry: 0, neutral: 0, hurt: 0 }; } else { for (const mood of ['angry', 'neutral', 'hurt']) { if (todaySectionData.moodClickCounts[mood] === undefined) { todaySectionData.moodClickCounts[mood] = 0; } } } } else { throw new Error("Invalid today data format"); } } catch (e) { console.error("Error loading Today data:", e); localStorage.removeItem(LS_TODAY_DATA_KEY); todaySectionData = { rating: 0, moods: { angry: 0, neutral: 0, hurt: 0 }, lastActivityDate: getLocalDateString(), moodClickCounts: { angry: 0, neutral: 0, hurt: 0 }, moodIcons: defaultIcons }; } }
            else { todaySectionData = { rating: 0, moods: { angry: 0, neutral: 0, hurt: 0 }, lastActivityDate: getLocalDateString(), moodClickCounts: { angry: 0, neutral: 0, hurt: 0 }, moodIcons: defaultIcons }; }
            const currentDateString = getLocalDateString();
            if (todaySectionData.lastActivityDate !== currentDateString) { todaySectionData.rating = 0; todaySectionData.moods = { angry: 0, neutral: 0, hurt: 0 }; todaySectionData.lastActivityDate = currentDateString; if (habits && habits.length > 0) { habits.forEach(h => h.lastIncrementDate = null); saveHabits(); } saveTodayData(); }
        }
        function renderTodayLeftPanel() {
            if (!todayCurrentRatingDisplay) return; todayCurrentRatingDisplay.textContent = (todaySectionData.rating > 0 ? '+' : '') + todaySectionData.rating; moodAngryCountDisplay.textContent = 'x' + todaySectionData.moods.angry; moodNeutralCountDisplay.textContent = 'x' + todaySectionData.moods.neutral; moodHurtCountDisplay.textContent = 'x' + todaySectionData.moods.hurt;
            moodAngryIcon.textContent = todaySectionData.moodIcons.angry; moodNeutralIcon.textContent = todaySectionData.moodIcons.neutral; moodHurtIcon.textContent = todaySectionData.moodIcons.hurt;
            todayRatingButtonsContainer.querySelectorAll('.rating-btn').forEach(btn => { btn.classList.remove('active'); if (parseInt(btn.dataset.value) === todaySectionData.rating) { btn.classList.add('active'); } });
        }
        todayRatingButtonsContainer.addEventListener('click', (event) => {
            if (event.target.classList.contains('rating-btn')) {
                const value = parseInt(event.target.dataset.value); todaySectionData.rating = value; todaySectionData.lastActivityDate = getLocalDateString(); saveTodayData(); renderTodayLeftPanel();
                const todayStr = getLocalDateString(); const todayRecord = getOrCreateRecordForDate(todayStr); todayRecord.todayRating = todaySectionData.rating; saveDailyRecords(); if (page3.classList.contains('active')) renderRecordsModule();
            }
        });
        moodIcons.forEach(icon => {
            icon.addEventListener('click', () => {
                const moodType = icon.dataset.mood;
                if (todaySectionData.moods.hasOwnProperty(moodType)) {
                    todaySectionData.moods[moodType]++; todaySectionData.lastActivityDate = getLocalDateString();
                    const todayStr = getLocalDateString(); const todayRecord = getOrCreateRecordForDate(todayStr); if (!todayRecord.moods) todayRecord.moods = { angry: 0, neutral: 0, hurt: 0 }; todayRecord.moods[moodType] = (todayRecord.moods[moodType] || 0) + 1; saveDailyRecords(); if (page3.classList.contains('active')) renderRecordsModule();
                    if (!todaySectionData.moodClickCounts) { todaySectionData.moodClickCounts = { angry: 0, neutral: 0, hurt: 0 }; }
                    if (todaySectionData.moodClickCounts[moodType] === undefined) { todaySectionData.moodClickCounts[moodType] = 0; }
                    todaySectionData.moodClickCounts[moodType]++;
                    if (todaySectionData.moodClickCounts[moodType] >= 15) { showMoodEasterEgg(icon.textContent); todaySectionData.moodClickCounts[moodType] = 0; }
                    saveTodayData(); renderTodayLeftPanel(); const countDisplay = document.getElementById(`mood-${moodType}-count`); if (countDisplay) { countDisplay.classList.add('animate-mood'); setTimeout(() => { countDisplay.classList.remove('animate-mood'); }, 300); }
                }
            });
        });
        todayTitle.addEventListener('click', openMoodCustomizationModal);
        function openMoodCustomizationModal() { moodIconInput1.value = todaySectionData.moodIcons.angry; moodIconInput2.value = todaySectionData.moodIcons.neutral; moodIconInput3.value = todaySectionData.moodIcons.hurt; moodCustomizationModal.style.display = 'flex'; }
        function closeMoodCustomizationModal() { moodCustomizationModal.style.display = 'none'; }
        function saveMoodCustomization() {
            const newIcon1 = moodIconInput1.value.trim(); const newIcon2 = moodIconInput2.value.trim(); const newIcon3 = moodIconInput3.value.trim();
            if (newIcon1 && newIcon2 && newIcon3) { todaySectionData.moodIcons.angry = newIcon1; todaySectionData.moodIcons.neutral = newIcon2; todaySectionData.moodIcons.hurt = newIcon3; saveTodayData(); renderTodayLeftPanel(); closeMoodCustomizationModal(); } else { alert('所有图标都不能为空。'); }
        }

        // --- Streak Module ---
        function saveStreakData() { localStorage.setItem(LS_STREAK_DATA_KEY, JSON.stringify(streakData)); }
        function loadStreakData() {
            const saved = localStorage.getItem(LS_STREAK_DATA_KEY);
            if (saved) { try { const parsed = JSON.parse(saved); if (parsed && typeof parsed.status === 'string') { streakData = { taskId: parsed.taskId || null, taskName: parsed.taskName || '', taskDesc: parsed.taskDesc || '', currentStreak: parsed.currentStreak || 0, longestStreak: parsed.longestStreak || 0, startDate: parsed.startDate ? new Date(parsed.startDate) : null, endDate: parsed.endDate ? new Date(parsed.endDate) : null, lastCompletedDate: parsed.lastCompletedDate ? new Date(parsed.lastCompletedDate) : null, status: parsed.status || 'NOT_STARTED', protectionPoints: parsed.protectionPoints !== undefined ? parsed.protectionPoints : 2, maxProtectionPoints: parsed.maxProtectionPoints || 2, protectionReplenishCounter: parsed.protectionReplenishCounter || 0, endedReason: parsed.endedReason || '' }; } else { throw new Error("Invalid streak data format"); } } catch (e) { console.error("Error loading Streak data:", e); localStorage.removeItem(LS_STREAK_DATA_KEY); streakData = { taskId: null, taskName: '', taskDesc: '', currentStreak: 0, longestStreak: 0, startDate: null, endDate: null, lastCompletedDate: null, status: 'NOT_STARTED', protectionPoints: 2, maxProtectionPoints: 2, protectionReplenishCounter: 0, endedReason: '' }; } }
        }
        function renderStreakModule() { if (!streakCountDisplay) return; streakCountDisplay.textContent = streakData.currentStreak; streakTaskNameDisplay.textContent = streakData.taskName || (streakData.status === 'NOT_STARTED' ? '未开始' : '无任务'); streakTaskNameDisplay.title = streakData.taskDesc || (streakData.taskName ? streakData.taskName : '没有选择任务'); streakProtectionPointsDisplay.textContent = `保护点: ${streakData.protectionPoints}/${streakData.maxProtectionPoints}`; streakAddChallengeBtn.style.display = 'none'; streakDoneBtn.style.display = 'none'; streakProtectBtn.style.display = 'none'; streakEndedBtn.style.display = 'none'; streakProtectionWarning.style.display = 'none'; streakProtectBtn.classList.remove('streak-protect-active'); switch (streakData.status) { case 'NOT_STARTED': streakAddChallengeBtn.style.display = 'flex'; streakTaskNameDisplay.classList.remove('clickable'); break; case 'ACTIVE': streakDoneBtn.style.display = 'flex'; streakTaskNameDisplay.classList.add('clickable'); const todayStr = new Date().toDateString(); if (streakData.lastCompletedDate && new Date(streakData.lastCompletedDate).toDateString() === todayStr) { streakDoneBtn.disabled = true; streakDoneBtn.textContent = '今日已完成'; } else { streakDoneBtn.disabled = false; streakDoneBtn.textContent = 'Done'; } break; case 'PROTECTION_PENDING': streakProtectBtn.style.display = 'flex'; streakTaskNameDisplay.classList.add('clickable'); streakProtectBtn.disabled = streakData.protectionPoints <= 0; streakProtectionWarning.textContent = '已错失一次打卡，保护后完成当天打卡则继续挑战'; streakProtectionWarning.style.display = 'block'; if (!streakProtectBtn.disabled) { streakProtectBtn.classList.add('streak-protect-active'); } break; case 'ENDED': streakEndedBtn.style.display = 'flex'; streakTaskNameDisplay.classList.add('clickable'); break; default: streakAddChallengeBtn.style.display = 'flex'; streakTaskNameDisplay.classList.remove('clickable'); break; } }
        function openAddChallengeModal() { challengeTodoSelect.innerHTML = '<option value="" disabled selected>请选择一个任务</option>'; if (todos && todos.length > 0) { todos.forEach(task => { if (task.levels && task.levels.length > 0) { const option = document.createElement('option'); option.value = task.id; option.textContent = `Lv${task.levels[0].level}: ${escapeHTML(task.levels[0].text.substring(0, 30))}${task.levels[0].text.length > 30 ? '...' : ''}`; challengeTodoSelect.appendChild(option); } }); } challengeNameInput.value = ''; challengeDescInput.value = ''; addChallengeModal.style.display = 'flex'; }
        function closeAddChallengeModal() { addChallengeModal.style.display = 'none'; }
        function openStreakDetailsModal() {
            if (!streakData || streakData.status === 'NOT_STARTED') return;
            detailsModalTaskName.textContent = streakData.taskName || 'N/A'; detailsModalTaskDesc.textContent = streakData.taskDesc || '无'; detailsModalCurrentStreak.textContent = streakData.currentStreak;
            let protectionInfoHTML = `<p>保护点: ${streakData.protectionPoints} / ${streakData.maxProtectionPoints}</p>`;
            if (streakData.status === 'ACTIVE' || streakData.status === 'PROTECTION_PENDING') { if (streakData.protectionPoints < streakData.maxProtectionPoints) { const daysNeeded = 5 - streakData.protectionReplenishCounter; protectionInfoHTML += `<p style="font-size:12px; color:#555;">再连续打卡 ${daysNeeded} 天可补充1点保护。</p>`; } else { protectionInfoHTML += `<p style="font-size:12px; color:#555;">保护点已满。</p>`; } }
            else if (streakData.status === 'ENDED') { protectionInfoHTML += `<p style="font-size:12px; color:#555;">挑战已结束。</p>`; }
            const statusInfoDiv = document.getElementById('details-modal-status-info');
            if (statusInfoDiv) { let baseHtml = `<p>当前连击: <span id="details-modal-current-streak">${streakData.currentStreak}</span></p>`; baseHtml += `<p id="details-modal-start-date-container" style="display:none;">开始日期: <span id="details-modal-start-date"></span></p>`; baseHtml += `<p id="details-modal-end-date-container" style="display:none;">结束日期: <span id="details-modal-end-date"></span></p>`; baseHtml += `<p id="details-modal-ended-reason" style="display:none;">结束原因: <span id="details-modal-reason-text"></span></p>`; statusInfoDiv.innerHTML = baseHtml + protectionInfoHTML; document.getElementById('details-modal-current-streak').textContent = streakData.currentStreak; }
            const newDetailsModalStartDateContainer = document.getElementById('details-modal-start-date-container'); const newDetailsModalStartDate = document.getElementById('details-modal-start-date'); const newDetailsModalEndDateContainer = document.getElementById('details-modal-end-date-container'); const newDetailsModalEndDate = document.getElementById('details-modal-end-date'); const newDetailsModalEndedReason = document.getElementById('details-modal-ended-reason'); const newDetailsModalReasonText = document.getElementById('details-modal-reason-text');
            streakDetailsInterruptBtn.style.display = 'none'; streakDetailsRestartBtn.style.display = 'none'; if (newDetailsModalEndedReason) newDetailsModalEndedReason.style.display = 'none'; if (newDetailsModalStartDateContainer) newDetailsModalStartDateContainer.style.display = 'none'; if (newDetailsModalEndDateContainer) newDetailsModalEndDateContainer.style.display = 'none';
            if (streakData.status === 'ACTIVE' || streakData.status === 'PROTECTION_PENDING') { streakDetailsModalTitle.textContent = '任务详情'; streakDetailsInterruptBtn.style.display = 'inline-block'; if (streakData.startDate && newDetailsModalStartDate && newDetailsModalStartDateContainer) { newDetailsModalStartDate.textContent = new Date(streakData.startDate).toLocaleDateString(); newDetailsModalStartDateContainer.style.display = 'block'; } }
            else if (streakData.status === 'ENDED') { streakDetailsModalTitle.textContent = '任务已结束'; if (newDetailsModalEndedReason && newDetailsModalReasonText) { newDetailsModalEndedReason.style.display = 'block'; newDetailsModalReasonText.textContent = streakData.endedReason === 'MISSED' ? '连续中断且保护耗尽' : (streakData.endedReason === 'INTERRUPTED' ? '手动中断' : '未知'); } streakDetailsRestartBtn.style.display = 'inline-block'; if (streakData.startDate && newDetailsModalStartDate && newDetailsModalStartDateContainer) { newDetailsModalStartDate.textContent = new Date(streakData.startDate).toLocaleDateString(); newDetailsModalStartDateContainer.style.display = 'block'; } if (streakData.endDate && newDetailsModalEndDate && newDetailsModalEndDateContainer) { newDetailsModalEndDate.textContent = new Date(streakData.endDate).toLocaleDateString(); newDetailsModalEndDateContainer.style.display = 'block'; } }
            streakTaskDetailsModal.style.display = 'flex';
        }
        function closeStreakDetailsModal() { streakTaskDetailsModal.style.display = 'none'; }
        streakAddChallengeBtn.addEventListener('click', openAddChallengeModal);
        streakTaskNameDisplay.addEventListener('click', () => { if (streakData.status !== 'NOT_STARTED' && streakTaskNameDisplay.classList.contains('clickable')) { openStreakDetailsModal(); } });
        function resetStreakDataToNotStarted() { streakData.taskId = null; streakData.taskName = ''; streakData.taskDesc = ''; streakData.currentStreak = 0; streakData.startDate = null; streakData.endDate = null; streakData.lastCompletedDate = null; streakData.status = 'NOT_STARTED'; streakData.protectionPoints = streakData.maxProtectionPoints; streakData.protectionReplenishCounter = 0; streakData.endedReason = ''; saveStreakData(); }
        saveChallengeBtn.addEventListener('click', () => { const selectedTodoId = challengeTodoSelect.value; let taskName = challengeNameInput.value.trim(); const taskDesc = challengeDescInput.value.trim(); if (!selectedTodoId) { alert('请从任务列表选择一个任务！'); return; } const selectedTodo = todos.find(t => t.id === selectedTodoId); if (selectedTodo && selectedTodo.levels && selectedTodo.levels.length > 0) { if (!taskName) { taskName = selectedTodo.levels[0].text; } streakData.taskId = selectedTodoId; } else { alert('选择的预设任务无效或已不存在。'); return; } if (!taskName) { alert('任务名称不能为空！'); return; } streakData.taskName = taskName; streakData.taskDesc = taskDesc; streakData.status = 'ACTIVE'; streakData.currentStreak = 0; streakData.startDate = new Date(); streakData.endDate = null; streakData.lastCompletedDate = null; streakData.protectionPoints = streakData.maxProtectionPoints; streakData.protectionReplenishCounter = 0; streakData.endedReason = ''; saveStreakData(); renderStreakModule(); closeAddChallengeModal(); });
        streakDoneBtn.addEventListener('click', () => { if (streakData.status !== 'ACTIVE') return; const today = new Date(); if (streakData.lastCompletedDate && new Date(streakData.lastCompletedDate).toDateString() === today.toDateString()) { alert('今日已打卡！'); return; } streakData.currentStreak++; if (streakData.currentStreak > streakData.longestStreak) { streakData.longestStreak = streakData.currentStreak; } streakData.lastCompletedDate = today; streakData.status = 'ACTIVE'; streakData.protectionReplenishCounter++; if (streakData.protectionReplenishCounter >= 5 && streakData.protectionPoints < streakData.maxProtectionPoints) { streakData.protectionPoints++; streakData.protectionReplenishCounter = 0; } saveStreakData(); renderStreakModule(); triggerAnimation(habitCelebration); });
        streakProtectBtn.addEventListener('click', () => { if (streakData.status !== 'PROTECTION_PENDING' || streakData.protectionPoints <= 0) return; streakData.protectionPoints--; streakData.status = 'ACTIVE'; const yesterday = new Date(); yesterday.setDate(yesterday.getDate() - 1); streakData.lastCompletedDate = yesterday; streakData.protectionReplenishCounter = 0; saveStreakData(); renderStreakModule(); });
        streakEndedBtn.addEventListener('click', openStreakDetailsModal);
        streakDetailsInterruptBtn.addEventListener('click', () => { if (confirm('确定要中断当前挑战吗？连击将清零。')) { streakData.status = 'ENDED'; streakData.endDate = new Date(); streakData.endedReason = 'INTERRUPTED'; saveStreakData(); renderStreakModule(); closeStreakDetailsModal(); } });
        streakDetailsRestartBtn.addEventListener('click', () => { if (confirm('确定要清零并重新开始吗？当前进度将丢失。')) { resetStreakDataToNotStarted(); renderStreakModule(); closeStreakDetailsModal(); } });
        function dailyStreakCheck() {
            if (!streakData || streakData.status === 'NOT_STARTED' || streakData.status === 'ENDED') { return; }
            const today = new Date(); const todayStr = today.toDateString(); let changed = false;
            if (streakData.status === 'ACTIVE') { if (!streakData.lastCompletedDate) { if (streakData.startDate && new Date(streakData.startDate).toDateString() !== todayStr) { streakData.status = 'PROTECTION_PENDING'; streakData.protectionReplenishCounter = 0; changed = true; } } else { const lastCompleted = new Date(streakData.lastCompletedDate); const lastCompletedStr = lastCompleted.toDateString(); if (lastCompletedStr !== todayStr) { const yesterday = new Date(); yesterday.setDate(today.getDate() - 1); const yesterdayStr = yesterday.toDateString(); if (lastCompletedStr !== yesterdayStr) { streakData.status = 'PROTECTION_PENDING'; streakData.protectionReplenishCounter = 0; changed = true; } } } }
            else if (streakData.status === 'PROTECTION_PENDING') { const lastActionDate = streakData.lastCompletedDate ? new Date(streakData.lastCompletedDate) : (streakData.startDate ? new Date(streakData.startDate) : new Date(0)); const oneDay = 24 * 60 * 60 * 1000; const diffDays = Math.round((today.getTime() - lastActionDate.getTime()) / oneDay); if (diffDays > 1) { streakData.status = 'ENDED'; streakData.endDate = new Date(); streakData.endedReason = 'MISSED'; changed = true; } }
            if (changed) { saveStreakData(); } renderStreakModule();
        }

        // --- Mood Easter Egg ---
        function showMoodEasterEgg(moodIconChar) {
            if (!moodEasterEggDiv) return;
            moodEasterEggDiv.textContent = moodIconChar; moodEasterEggDiv.style.display = 'block'; moodEasterEggDiv.style.transform = 'translate(-50%, -50%) scale(0.5)';
            setTimeout(() => { moodEasterEggDiv.style.opacity = '1'; moodEasterEggDiv.style.transform = 'translate(-50%, -50%) scale(1)'; }, 50);
            setTimeout(() => { moodEasterEggDiv.style.opacity = '0'; moodEasterEggDiv.style.transform = 'translate(-50%, -50%) scale(0.5)'; setTimeout(() => { moodEasterEggDiv.style.display = 'none'; }, 300); }, 1200);
        }

        // --- Records Module ---
        function renderRecordsModule() {
            if (!recordsChartArea) return;
            let recordsToDisplay = []; const daysOfWeek = ['日', '一', '二', '三', '四', '五', '六'];
            recordsPrevMonthBtn.style.display = 'none'; recordsNextMonthBtn.style.display = 'none';
            if (currentRecordView === 'week') {
                recordViewWeekBtn.classList.add('active'); recordViewMonthBtn.classList.remove('active');
                const today = new Date();
                for (let i = 6; i >= 0; i--) {
                    const date = new Date(today); date.setDate(today.getDate() - i); const dateString = getLocalDateString(date);
                    const record = getRecordForDate(dateString) || { date: dateString, tasksCompleted: 0, habitsIncremented: 0, todayRating: 0, moods: { angry: 0, neutral: 0, hurt: 0 } };
                    if (!record.moods) record.moods = { angry: 0, neutral: 0, hurt: 0 };
                    recordsToDisplay.push({ ...record, dayOfWeek: daysOfWeek[date.getDay()] });
                }
            } else { // Month View
                recordViewMonthBtn.classList.add('active'); recordViewWeekBtn.classList.remove('active');
                recordsPrevMonthBtn.style.display = 'block'; recordsNextMonthBtn.style.display = 'block';
                const year = chartDisplayDate.getFullYear(); const month = chartDisplayDate.getMonth(); const daysInMonth = new Date(year, month + 1, 0).getDate();
                const firstDayOfMonth = new Date(year, month, 1);
                for (let i = 0; i < daysInMonth; i++) {
                    const date = new Date(firstDayOfMonth); date.setDate(firstDayOfMonth.getDate() + i); const dateString = getLocalDateString(date);
                    const record = getRecordForDate(dateString) || { date: dateString, tasksCompleted: 0, habitsIncremented: 0, todayRating: 0, moods: { angry: 0, neutral: 0, hurt: 0 } };
                    if (!record.moods) record.moods = { angry: 0, neutral: 0, hurt: 0 };
                    recordsToDisplay.push({ ...record, dayOfWeek: daysOfWeek[date.getDay()] });
                }
                const today = new Date(); recordsNextMonthBtn.disabled = chartDisplayDate.getFullYear() >= today.getFullYear() && chartDisplayDate.getMonth() >= today.getMonth();
            }
            drawRecordsChart(recordsToDisplay);
        }
        function drawRecordsChart(records) {
            recordsChartArea.innerHTML = ''; recordsChartArea.appendChild(recordsPrevMonthBtn); recordsChartArea.appendChild(recordsNextMonthBtn);
            const hasRecords = records.some(r => r.tasksCompleted > 0 || r.habitsIncremented > 0 || r.todayRating !== 0 || r.moods.angry > 0 || r.moods.neutral > 0 || r.moods.hurt > 0);
            if (!hasRecords) { if (recordsPlaceholderMsg) { recordsChartArea.appendChild(recordsPlaceholderMsg); recordsPlaceholderMsg.style.display = 'block'; } return; }
            if (recordsPlaceholderMsg) recordsPlaceholderMsg.style.display = 'none';
            const maxTaskHabitValue = Math.max(5, ...records.map(r => r.tasksCompleted), ...records.map(r => r.habitsIncremented)); const maxRatingMagnitude = 2;
            records.forEach(record => {
                const dayGroup = document.createElement('div'); dayGroup.className = 'records-chart-day-group';
                const barsContainer = document.createElement('div'); barsContainer.className = 'records-chart-day-bars';
                const taskBar = document.createElement('div'); taskBar.className = 'records-chart-bar tasks'; const taskHeight = record.tasksCompleted > 0 ? Math.max(5, (record.tasksCompleted / maxTaskHabitValue) * 100) : 0; taskBar.style.height = `${taskHeight}%`; taskBar.title = `任务: ${record.tasksCompleted}`; if (currentRecordView !== 'week' && record.tasksCompleted > 0) { const taskValueSpan = document.createElement('span'); taskValueSpan.textContent = record.tasksCompleted; taskBar.appendChild(taskValueSpan); }
                const habitBar = document.createElement('div'); habitBar.className = 'records-chart-bar habits'; const habitHeight = record.habitsIncremented > 0 ? Math.max(5, (record.habitsIncremented / maxTaskHabitValue) * 100) : 0; habitBar.style.height = `${habitHeight}%`; habitBar.title = `习惯: ${record.habitsIncremented}`; if (currentRecordView !== 'week' && record.habitsIncremented > 0) { const habitValueSpan = document.createElement('span'); habitValueSpan.textContent = record.habitsIncremented; habitBar.appendChild(habitValueSpan); }
                const ratingBar = document.createElement('div'); const ratingValue = record.todayRating || 0; let ratingHeight; if (ratingValue === 0) { ratingBar.className = 'records-chart-bar rating-zero'; ratingHeight = 2; } else { ratingBar.className = `records-chart-bar ${ratingValue > 0 ? 'rating-positive' : 'rating-negative'}`; ratingHeight = (Math.abs(ratingValue) / maxRatingMagnitude) * 50; } ratingBar.style.height = `${ratingHeight}%`;
                const moodCounts = record.moods || { angry: 0, neutral: 0, hurt: 0 }; ratingBar.title = `评分: ${ratingValue}\n${todaySectionData.moodIcons.angry}: ${moodCounts.angry} | ${todaySectionData.moodIcons.neutral}: ${moodCounts.neutral} | ${todaySectionData.moodIcons.hurt}: ${moodCounts.hurt}`; if (currentRecordView !== 'week' && ratingValue !== 0) { const ratingValueSpan = document.createElement('span'); ratingValueSpan.textContent = ratingValue; ratingBar.appendChild(ratingValueSpan); }
                barsContainer.appendChild(taskBar); barsContainer.appendChild(habitBar); barsContainer.appendChild(ratingBar); dayGroup.appendChild(barsContainer);
                const dateLabel = document.createElement('div'); dateLabel.className = 'records-chart-date-label'; const dateObj = new Date(record.date + 'T00:00:00'); dateLabel.textContent = `${dateObj.getMonth() + 1}/${dateObj.getDate()}`; dayGroup.appendChild(dateLabel);
                const dayOfWeekLabel = document.createElement('div'); dayOfWeekLabel.className = 'records-chart-day-label'; dayOfWeekLabel.textContent = record.dayOfWeek; dayGroup.appendChild(dayOfWeekLabel);
                recordsChartArea.appendChild(dayGroup);
            });
        }
        recordViewWeekBtn.addEventListener('click', () => { currentRecordView = 'week'; chartDisplayDate = new Date(); renderRecordsModule(); });
        recordViewMonthBtn.addEventListener('click', () => { currentRecordView = 'month'; renderRecordsModule(); });
        clearRecordsBtn.addEventListener('click', () => { if (confirm("确定要清空所有回顾记录吗？此操作不可撤销，且不会影响其他模块数据。")) { dailyRecords = []; saveDailyRecords(); renderRecordsModule(); } });
        recordsPrevMonthBtn.addEventListener('click', () => { chartDisplayDate.setMonth(chartDisplayDate.getMonth() - 1); renderRecordsModule(); });
        recordsNextMonthBtn.addEventListener('click', () => { chartDisplayDate.setMonth(chartDisplayDate.getMonth() + 1); renderRecordsModule(); });

        document.addEventListener('DOMContentLoaded', initializeApp);

    </script>
</body>

</html>