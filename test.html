<!DOCTYPE html>
<html>
<head>
    <title>测试页面</title>
</head>
<body>
    <h1>应用修复测试</h1>
    <p>如果你能看到这个页面，说明服务器正在运行。</p>
    <p><a href="index.html">点击这里访问主应用</a></p>
    
    <script>
        console.log('测试页面加载成功');
        
        // 测试主应用是否可以加载
        fetch('index.html')
            .then(response => {
                if (response.ok) {
                    console.log('主应用文件可以正常访问');
                } else {
                    console.error('主应用文件访问失败');
                }
            })
            .catch(error => {
                console.error('网络错误:', error);
            });
    </script>
</body>
</html>
